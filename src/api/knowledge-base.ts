import { ApiService, API_ENDPOINTS } from "../lib/api";
import {
  KnowledgeBase,
  KnowledgeBaseCreate,
  KnowledgeBaseUpdate,
  KnowledgeBaseResponse,
  KnowledgeBaseList,
  KnowledgeBaseFilters,
  BulkUploadResponse,
  KnowledgeBaseFilesResponse,
} from "@/types/knowledge-base";

export const knowledgeBaseAPI = {
  // Create a new knowledge base with files
  async createKnowledgeBase(data: KnowledgeBaseCreate): Promise<KnowledgeBaseResponse> {
    const formData = new FormData();

    formData.append("knowledgebase_name", data.knowledgebase_name);
    if (data.folder_description) {
      formData.append("folder_description", data.folder_description);
    }
    if (data.chunking_config) {
      formData.append("chunking_config", data.chunking_config);
    }
    if (data.embedding_config) {
      formData.append("embedding_config", data.embedding_config);
    }
    if (data.vector_db_config) {
      formData.append("vector_db_config", data.vector_db_config);
    }
    if (data.files) {
      data.files.forEach((file) => {
        formData.append("files", file);
      });
    }

    const response = await ApiService.post<KnowledgeBaseResponse>(API_ENDPOINTS.PRIVATE.KNOWLEDGE_BASE + "/create-folder-and-uploads", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response;
  },

  // Get all knowledge bases
  async getKnowledgeBases(filters?: KnowledgeBaseFilters): Promise<KnowledgeBaseList> {
    const params = new URLSearchParams();

    if (filters?.skip) params.append("skip", filters.skip.toString());
    if (filters?.limit) params.append("limit", filters.limit.toString());
    if (filters?.sort_by) params.append("sort_by", filters.sort_by);
    if (filters?.sort_order) params.append("sort_order", filters.sort_order);
    if (filters?.name) params.append("name", filters.name);
    if (filters?.name_like) params.append("name_like", filters.name_like);
    if (filters?.is_active !== undefined) params.append("is_active", filters.is_active.toString());

    const response = await ApiService.get<KnowledgeBaseList>(`${API_ENDPOINTS.PRIVATE.KNOWLEDGE_BASE}?${params.toString()}`);
    return response;
  },

  // Get a specific knowledge base by ID
  async getKnowledgeBase(id: string): Promise<KnowledgeBase> {
    const response = await ApiService.get<KnowledgeBase>(`${API_ENDPOINTS.PRIVATE.KNOWLEDGE_BASE}/${id}`);
    return response;
  },

  // Update a knowledge base
  async updateKnowledgeBase(id: string, data: KnowledgeBaseUpdate): Promise<KnowledgeBase> {
    const response = await ApiService.put<KnowledgeBase>(`${API_ENDPOINTS.PRIVATE.KNOWLEDGE_BASE}/${id}`, data);
    return response;
  },

  // Delete a knowledge base
  async deleteKnowledgeBase(id: string, permanent: boolean = false): Promise<{ success: boolean }> {
    const params = permanent ? "?permanent=true" : "";
    const response = await ApiService.delete<{ success: boolean }>(`${API_ENDPOINTS.PRIVATE.KNOWLEDGE_BASE}/${id}${params}`);
    return response;
  },

  // Restore a soft-deleted knowledge base
  async restoreKnowledgeBase(id: string): Promise<KnowledgeBase> {
    const response = await ApiService.post<KnowledgeBase>(`${API_ENDPOINTS.PRIVATE.KNOWLEDGE_BASE}/${id}/restore`);
    return response;
  },

  // Get files in a knowledge base
  async getKnowledgeBaseFiles(folderId: string): Promise<KnowledgeBaseFilesResponse> {
    const response = await ApiService.get<KnowledgeBaseFilesResponse>(`${API_ENDPOINTS.PRIVATE.KNOWLEDGE_BASE}/${folderId}/files`);
    return response;
  },

  // Download a file
  async downloadFile(fileId: string): Promise<Blob> {
    const response = await ApiService.get<Blob>(`${API_ENDPOINTS.PRIVATE.KNOWLEDGE_BASE}/file/download/${fileId}`, {
      responseType: "blob",
    });
    return response;
  },

  // Generate presigned URL for file access
  async generatePresignedUrl(fileId: string) {
    const response = await ApiService.get(`${API_ENDPOINTS.PRIVATE.KNOWLEDGE_BASE}/generate-url/${fileId}`);
    return response;
  },

  // Delete a file from S3 (using S3 service)
  async deleteFile(fileId: string): Promise<{ success: boolean; message: string }> {
    const response = await ApiService.delete<{ success: boolean; message: string }>(`/private/s3/${fileId}`);
    return response;
  },

  // Bulk upload files to existing knowledge base
  async bulkUploadFiles(folderId: string, files: File[]): Promise<BulkUploadResponse> {
    const formData = new FormData();
    formData.append("folder_id", folderId);

    files.forEach((file) => {
      formData.append("files", file);
    });

    const response = await ApiService.post<BulkUploadResponse>(`/private/s3/upload/bulk`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response;
  },
};
