import axios from "axios";
import { get_client_env } from "@/lib/env";
import {
    EnvironmentVariableCreate,
    EnvironmentVariableUpdate,
    EnvironmentVariableOut,
    PaginatedEnvVarResponse,
} from "@/types/settings";

const getHeaders = () => {
    const token_type = localStorage.getItem("token_type");
    const access_token = localStorage.getItem("access_token");
    return { Authorization: `${token_type} ${access_token}` };
};

const env = get_client_env();
const ENV_URL = `${env.backend_url}/private/envs`;

export const envVarAPI = {
    // Get environment variables for the current user (paginated)
    getEnvVars: async (params?: { skip?: number; limit?: number; name_like?: string; sort?: string; sort_order?: string }): Promise<PaginatedEnvVarResponse> => {
        const { skip = 0, limit = 20, name_like = "", sort = "name", sort_order = "asc" } = params || {};
        const response = await axios.get(ENV_URL + "/", {
            headers: getHeaders(),
            params: { skip, limit, name_like, sort, sort_order },
        });
        return response.data;
    },

    // Create environment variable (user_id not needed)
    createEnvVar: async (data: Omit<EnvironmentVariableCreate, "user_id">): Promise<EnvironmentVariableOut> => {
        const response = await axios.post(`${ENV_URL}/`, data, {
            headers: getHeaders(),
        });
        return response.data;
    },

    // Update environment variable
    updateEnvVar: async (env_var_id: string, data: EnvironmentVariableUpdate): Promise<EnvironmentVariableOut> => {
        const response = await axios.put(`${ENV_URL}/${env_var_id}`, data, {
            headers: getHeaders(),
        });
        return response.data;
    },

    // Delete environment variable (hard delete)
    deleteEnvVar: async (env_var_id: string): Promise<{ success: boolean }> => {
        const response = await axios.delete(`${ENV_URL}/${env_var_id}/hard`, {
            headers: getHeaders(),
        });
        return response.data;
    },

    // Get a single environment variable by id
    getEnvVarById: async (env_var_id: string): Promise<EnvironmentVariableOut> => {
        const response = await axios.get(`${ENV_URL}/${env_var_id}`, {
            headers: getHeaders(),
        });
        return response.data;
    },
};
