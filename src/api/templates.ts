import { ApiService } from "../lib/api";
import {
  TemplateCreateRequest,
  TemplateUpdate,
  TemplateResponse,
  TemplateListResponse,
  TemplateFilters,
  TemplateInstantiationRequest,
  TemplateInstantiationResponse,
} from "../types/template";

export const templatesApi = {
  // Get all templates with filtering (using existing backend endpoint)
  getAll: (params?: TemplateFilters): Promise<TemplateListResponse> => {
    const searchParams = new URLSearchParams();

    if (params?.skip !== undefined)
      searchParams.append("skip", params.skip.toString());
    if (params?.limit !== undefined)
      searchParams.append("limit", params.limit.toString());
    if (params?.sort_by && params.sort_by.length > 0) {
      params.sort_by.forEach((field) => searchParams.append("sort_by", field));
    }
    if (params?.sort_order)
      searchParams.append("sort_order", params.sort_order);
    if (params?.category) searchParams.append("category", params.category);
    if (params?.tags && params.tags.length > 0) {
      params.tags.forEach((tag) => searchParams.append("tags", tag));
    }
    if (params?.search) searchParams.append("name_like", params.search); // Backend uses name_like for search

    const queryString = searchParams.toString();
    const url = `/private/team-templates${queryString ? `?${queryString}` : ""}`;

    return ApiService.get<TemplateListResponse>(url);
  },

  // Get template by ID
  getById: (id: string): Promise<TemplateResponse> => {
    return ApiService.get<TemplateResponse>(`/private/team-templates/${id}`);
  },

  // Create new template
  create: (data: TemplateCreateRequest): Promise<TemplateResponse> => {
    return ApiService.post<TemplateResponse>("/private/team-templates", data);
  },

  // Update template
  update: (id: string, data: TemplateUpdate): Promise<TemplateResponse> => {
    return ApiService.put<TemplateResponse>(`/private/team-templates/${id}`, data);
  },

  // Delete template (soft delete)
  delete: (id: string): Promise<{ success: boolean }> => {
    return ApiService.delete<{ success: boolean }>(
      `/private/team-templates/${id}`
    );
  },

  // Get template API requirements
  getApiRequirements: (templateId: string): Promise<{
    required_providers: string[];
    agent_requirements: Array<{
      provider_type: string;
      provider_name: string;
      context_type: string;
      context_name: string;
      required_fields: string[];
      optional_fields: string[];
      missing_fields: string[];
      present_fields: string[];
      model: string;
    }>;
    team_requirements: {
      provider_type: string;
      provider_name: string;
      context_type: string;
      context_name: string;
      required_fields: string[];
      optional_fields: string[];
      missing_fields: string[];
      present_fields: string[];
      model: string;
    } | null;
  }> => {
    return ApiService.get(`/private/team-templates/${templateId}/api-requirements`);
  },

  // Instantiate template as a workflow/team (using existing backend endpoint)
  instantiate: (data: TemplateInstantiationRequest): Promise<TemplateInstantiationResponse> => {
    const payload = {
      template_id: data.template_id,
      name: data.name,
      customizations: data.customizations || {},
    };

    return ApiService.post<any>("/private/teams/from-template", payload)
      .then((teamResponse) => ({
        team_id: teamResponse.id,
        template_id: data.template_id,
        status: "success" as const,
      }));
  },

  // Generate customizations object from API keys
  // This helper creates the proper nested structure for model_client configurations
  generateApiKeyCustomizations: (
    apiRequirements: any,
    apiKeys: Record<string, Record<string, string>>
  ): Record<string, any> => {
    const customizations: any = {
      config: {
        participants: [],
      },
    };

    // Handle team-level model client
    if (apiRequirements.team_requirements) {
      const teamUniqueKey = `team_${apiRequirements.team_requirements.provider_type}`;
      if (apiKeys[teamUniqueKey]) {
        customizations.config.model_client = {
          config: apiKeys[teamUniqueKey],
        };
      }
    }

    // Handle agent-level model clients
    apiRequirements.agent_requirements.forEach((req: any, index: number) => {
      const agentUniqueKey = `agent_${index}_${req.provider_type}`;
      if (apiKeys[agentUniqueKey]) {
        // Ensure participants array has enough entries
        while (customizations.config.participants.length <= index) {
          customizations.config.participants.push({});
        }

        customizations.config.participants[index] = {
          config: {
            model_client: {
              config: apiKeys[agentUniqueKey],
            },
          },
        };
      }
    });

    return customizations;
  },
};