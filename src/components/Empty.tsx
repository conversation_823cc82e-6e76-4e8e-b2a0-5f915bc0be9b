import { Inbox } from "lucide-react";

interface EmptyProps {
    title: string;
    description: string;
}

export const Empty = ({ title, description }: EmptyProps) => {
    return (
        <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-6">
                <Inbox className="w-12 h-12 text-slate-400" />
            </div>
            <h3 className="text-xl font-semibold text-slate-900 mb-2">
                {title}
            </h3>
            <p className="text-slate-600 max-w-md">
                {description}
            </p>
        </div>
    );
};
