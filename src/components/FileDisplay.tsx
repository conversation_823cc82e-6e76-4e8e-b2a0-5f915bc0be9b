import React from "react";
import FileIcon from "@/components/FileIcon";
import { cn } from "@/lib/utils";

interface FileDisplayProps {
  fileName: string;
  mimeType?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
  truncate?: boolean;
  showExtension?: boolean;
  fileIconColor?: string;
}

const FileDisplay: React.FC<FileDisplayProps> = ({
  fileName,
  mimeType,
  size = "md",
  className = "",
  truncate = true,
  showExtension = true,
  fileIconColor,
}) => {
  const displayName = showExtension
    ? fileName
    : fileName.split(".").slice(0, -1).join(".");

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <FileIcon
        mimeType={mimeType}
        fileName={fileName}
        size={size}
        className="flex-shrink-0"
        color={fileIconColor}
      />
      <span
        className={cn("text-sm font-medium text-black", truncate && "truncate")}
      >
        {displayName}
      </span>
    </div>
  );
};

export default FileDisplay;
