import React from "react";
import {
  getFileTypeIcon,
  getMimeTypeFromExtension,
} from "@/constants/fileTypes";
import { File } from "lucide-react";

interface FileIconProps {
  mimeType?: string;
  fileName?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
  color?: string;
}

const FileIcon: React.FC<FileIconProps> = ({
  mimeType,
  fileName,
  size = "md",
  className = "",
  color,
}) => {
  // Determine icon size based on size prop
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6",
  };

  // Get the appropriate icon
  let IconComponent = File;

  if (mimeType) {
    IconComponent = getFileTypeIcon(mimeType);
  } else if (fileName) {
    // Try to determine mime type from file extension if mimeType not provided
    const extension = fileName.split(".").pop()?.toLowerCase();
    if (extension) {
      const detectedMimeType = getMimeTypeFromExtension(extension);
      if (detectedMimeType) {
        IconComponent = getFileTypeIcon(detectedMimeType);
      }
    }
  }

  return (
    <IconComponent
      className={`${sizeClasses[size]} ${color || "text-teal-600"} ${className}`}
    />
  );
};

export default FileIcon;
