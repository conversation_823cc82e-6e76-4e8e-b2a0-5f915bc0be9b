import { Trash2, Edit, GitFork } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Agent2 } from "@/types/agent";
import TooltipText from "../TooltipText";
import DeleteAgentModal from "./DeleteAgentModal";
import { useState } from "react";

interface AgentCardProps {
  agent: Agent2;
  onClick: (agent: Agent2) => void;
  onDelete: (agentId: string) => void;
  onFork?: (agent: Agent2) => void; // New prop for forking default agents
  isDeleting?: boolean;
}

export const AgentCard: React.FC<AgentCardProps> = ({
  agent,
  onClick,
  onDelete,
  onFork,
  isDeleting = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleDelete = () => onDelete(agent.id);

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick(agent);
  };

  const handleFork = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onFork) {
      onFork(agent);
    }
  };

  return (
    <Card
      className={`hover:shadow-lg transition-all duration-300 bg-white border-slate-200 border-2 overflow-hidden group h-[240px] ${
        agent.is_default ? "cursor-default" : "cursor-pointer"
      }`}
    >
      <CardContent className="p-6 flex flex-col h-full justify-between">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex flex-col gap-2 max-w-[85%]">
              <h3 className="text-lg font-bold text-slate-900 transition-colors leading-tight truncate">
                {agent.name}
              </h3>
              <div className="flex flex-row gap-2">
                {agent.is_default && (
                  <Badge
                    variant="secondary"
                    className="w-fit bg-blue-50 text-blue-700 border-blue-200 text-xs"
                  >
                    Default
                  </Badge>
                )}
                <Badge
                  variant="secondary"
                  className="w-fit bg-green-50 text-green-700 border-green-200 text-xs"
                >
                  {agent.agentType}
                </Badge>
                <Badge
                  variant="secondary"
                  className="w-fit bg-green-50 text-green-700 border-green-200 text-xs"
                >
                  {agent.modelClient}
                </Badge>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <p className="text-slate-600 text-sm leading-relaxed line-clamp-3">
              {agent.description || "No description available"}
            </p>
          </div>
        </div>

        <div className="flex items-center justify-between pt-4 border-t border-slate-100">
          <div className="text-xs text-slate-400">
            Modified {agent.lastModified}
          </div>

          <div>
            {agent.is_default ? (
              // For default agents: show Fork & Edit option
              <TooltipText text="Fork & Edit">
                <Button size="icon" variant="ghost" onClick={handleFork}>
                  <GitFork className="h-4 w-4" />
                </Button>
              </TooltipText>
            ) : (
              // For non-default agents: show Edit and Delete options
              <>
                <TooltipText text="Edit">
                  <Button size="icon" variant="ghost" onClick={handleEdit}>
                    <Edit className="h-4 w-4" />
                  </Button>
                </TooltipText>
                <TooltipText text="Delete">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="text-red-600"
                    disabled={isDeleting}
                    onClick={() => setIsOpen(true)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                  <DeleteAgentModal
                    isOpen={isOpen}
                    setIsOpen={setIsOpen}
                    agent={agent}
                    onDelete={handleDelete}
                  />
                </TooltipText>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
