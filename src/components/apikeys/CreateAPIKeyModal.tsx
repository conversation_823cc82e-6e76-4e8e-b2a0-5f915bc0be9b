import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Des<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Check, ChevronsUpDown } from "lucide-react";
import { useAPIKeys } from "@/contexts/APIKeyContext";
import React from "react";
import { cn, toTitleCase } from "@/lib/utils";

export default function CreateAPIKeyModal({
  formRef,
  handleSubmit,
  open,
  setOpen,
  selectedScopes,
  setSelectedScopes,
  children,
}: {
  formRef: React.RefObject<HTMLFormElement>;
  handleSubmit: (e: React.FormEvent) => void;
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedScopes: string[];
  setSelectedScopes: React.Dispatch<React.SetStateAction<string[]>>;
  children: React.ReactNode;
}) {
  const { scopes: scopesData } = useAPIKeys();

  const [openPermissionPopover, setOpenPermissionPopover] = React.useState<
    string | null
  >(null);

  const onScopeSelect = (currentValue: string) => {
    setSelectedScopes((prev) => {
      const newScopes = [...prev];

      // remove the default all permissions
      if (newScopes.includes("*")) {
        newScopes.splice(newScopes.indexOf("*"), 1);
      }
      if (newScopes.includes(currentValue)) {
        newScopes.splice(newScopes.indexOf(currentValue), 1);
      }
      return newScopes;
    });
  };

  const scope_types: string[] = [];
  scopesData.forEach((scope) => {
    if (scope.scope === "*") return;
    if (scope.scope.startsWith("write")) return;

    scope_types.push(toTitleCase(scope.scope.split(":").at(-1)));
  });

  const handlePermissionChange = (
    permission: string,
    read_scope: string,
    write_scope: string
  ) => {
    setSelectedScopes((prev) => {
      const newScopes = prev.filter(
        (s) => s !== read_scope && s !== write_scope && s !== "*"
      );
      if (permission === "read") {
        newScopes.push(read_scope);
      } else if (permission === "write") {
        newScopes.push(write_scope);
      } else if (permission === "read_write") {
        newScopes.push(read_scope, write_scope);
      }
      return newScopes;
    });
    setOpenPermissionPopover(null); // close popover after click
  };

  const toggleScope = (scope: string) => {
    setSelectedScopes((prev) => {
      const newScopes = [...prev];
      if (newScopes.includes(scope)) {
        newScopes.splice(newScopes.indexOf(scope), 1);
      } else {
        newScopes.push(scope);
      }
      return newScopes;
    });
  };

  const dropdown_options = [
    { label: "Read", value: "read" },
    { label: "Write", value: "write" },
    { label: "Read & Write", value: "read_write" },
  ];

  const onOpenChange = (open: boolean) => {
    if (!open) {
      // on close
      setSelectedScopes([]);
    }

    setOpen(open);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New API Key</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <form ref={formRef} onSubmit={handleSubmit}>
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="name">
                <strong>Name</strong>
              </Label>
              <Input id="name" name="name" placeholder="My Test Key" />
            </div>
            <div>
              <Label htmlFor="permissions">
                <strong>Permissions</strong>
              </Label>
              <Tabs defaultValue="all" className="">
                <TabsList>
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="read">Read</TabsTrigger>
                  <TabsTrigger value="write">Write</TabsTrigger>
                  {/* <TabsTrigger value="restricted">Password</TabsTrigger> */}
                </TabsList>
                <div className="flex flex-col gap-2 rounded-lg border border-border py-2 px-4 mt-4 w-full">
                  <div className="flex justify-between">
                    <h4 className="font-medium text-xs leading-5">Resources</h4>
                    <h4 className="font-medium text-xs leading-5">
                      Permissions
                    </h4>
                  </div>
                  <TabsContent value="all">
                    <ScrollArea className="h-[200px]">
                      <div className="flex flex-col gap-2">
                        {scope_types.map((scope) => {
                          const read_scope = `read:${scope.toLowerCase()}`;
                          const write_scope = `write:${scope.toLowerCase()}`;

                          let selected_permission = "none";
                          if (
                            selectedScopes.includes(read_scope) &&
                            selectedScopes.includes(write_scope)
                          ) {
                            selected_permission = "read_write";
                          } else if (selectedScopes.includes(write_scope)) {
                            selected_permission = "write";
                          } else if (selectedScopes.includes(read_scope)) {
                            selected_permission = "read";
                          }

                          return (
                            <div
                              className="flex justify-between items-center bg-muted px-2 rounded-lg"
                              key={scope}
                            >
                              <Label
                                htmlFor={scope}
                                className="text-sm leading-5"
                              >
                                {scope}
                              </Label>
                              <Popover
                                open={openPermissionPopover === scope}
                                onOpenChange={(open) =>
                                  setOpenPermissionPopover(open ? scope : null)
                                }
                              >
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    type="button"
                                    className="flex items-center gap-2 px-3 py-1 rounded-md min-w-[120px] justify-end"
                                  >
                                    <span>
                                      {selected_permission === "none"
                                        ? "None"
                                        : selected_permission === "read"
                                        ? "Read"
                                        : selected_permission === "write"
                                        ? "Write"
                                        : "Read & Write"}
                                    </span>
                                    <ChevronsUpDown className="h-4 w-4 text-gray-400" />
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="p-0 w-48">
                                  <div className="flex flex-col">
                                    {dropdown_options.map((option) => (
                                      <Button
                                        variant="ghost"
                                        type="button"
                                        key={option.value}
                                        className={cn(
                                          "flex items-center justify-between px-4 py-2 text-left w-full",
                                          selected_permission ===
                                            option.value && "font-semibold"
                                        )}
                                        onClick={() =>
                                          handlePermissionChange(
                                            option.value,
                                            read_scope,
                                            write_scope
                                          )
                                        }
                                      >
                                        <span>{option.label}</span>
                                        {selected_permission ===
                                          option.value && (
                                          <Check className="h-4 w-4 text-teal-700" />
                                        )}
                                      </Button>
                                    ))}
                                  </div>
                                </PopoverContent>
                              </Popover>
                            </div>
                          );
                        })}
                      </div>
                      <ScrollBar orientation="vertical" />
                    </ScrollArea>
                  </TabsContent>
                  <TabsContent value="read">
                    <ScrollArea className="h-[200px]">
                      <div className="flex flex-col gap-2">
                        {scope_types.map((scope) => {
                          const read_scope = `read:${scope.toLowerCase()}`;

                          const displayCheck =
                            selectedScopes.includes(read_scope);

                          const onClick = () => toggleScope(read_scope);

                          return (
                            <div
                              onClick={onClick}
                              className="cursor-pointer flex justify-between items-center bg-muted px-2 rounded-lg hover:bg-muted/80 py-2.5"
                              key={scope}
                            >
                              <Label
                                htmlFor={scope}
                                className="text-sm leading-5"
                              >
                                {scope}
                              </Label>
                              {displayCheck && <Check className="h-4 w-4" />}
                            </div>
                          );
                        })}
                      </div>
                    </ScrollArea>
                  </TabsContent>
                  <TabsContent value="write">
                    <ScrollArea className="h-[200px]">
                      <div className="flex flex-col gap-2">
                        {scope_types.map((scope) => {
                          const write_scope = `write:${scope.toLowerCase()}`;

                          const displayCheck =
                            selectedScopes.includes(write_scope);

                          const onClick = () => toggleScope(write_scope);

                          return (
                            <div
                              onClick={onClick}
                              className="cursor-pointer flex justify-between items-center bg-muted px-2 rounded-lg hover:bg-muted/80 py-2.5"
                              key={scope}
                            >
                              <Label
                                htmlFor={scope}
                                className="text-sm leading-5"
                              >
                                {scope}
                              </Label>
                              {displayCheck && <Check className="h-4 w-4" />}
                            </div>
                          );
                        })}
                      </div>
                    </ScrollArea>
                  </TabsContent>
                </div>
              </Tabs>
            </div>
          </div>
        </form>
        <DialogFooter>
          <Button
            onClick={() => formRef.current?.requestSubmit()}
            type="submit"
            className="bg-teal-800 hover:bg-teal-700 text-white"
          >
            Create API Key
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
