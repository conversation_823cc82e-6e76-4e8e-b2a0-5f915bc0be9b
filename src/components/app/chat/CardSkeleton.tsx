import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import Sparkle from "@/components/icons/sparkle";

interface CardSkeletonProps {
  teamLabel?: string;
}

const CardSkeleton = ({ teamLabel }: CardSkeletonProps) => {
  return (
    <div className="flex justify-start">
      <div className="flex items-start gap-3">
        <Card className="bg-white rounded-[20px] w-[400px]">
          <CardContent className="p-5 px-8 space-y-4">
            <div className="flex justify-start space-x-2 items-center">
              <div className="w-8 h-8 rounded-full flex items-center justify-center bg-teal-800 text-white">
                <Sparkle className="h-4 w-4" />
              </div>
              <h4 className="text-sm font-semibold leading-[130%]">
                {teamLabel || "Loading..."}
              </h4>
            </div>
            <Skeleton className="h-4 w-full rounded-full" />
            <Skeleton className="h-4 w-full rounded-full" />
            <div className="text-xs mt-2 text-teal-100"></div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CardSkeleton;
