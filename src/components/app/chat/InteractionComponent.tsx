import { useChatMessageContext } from "@/contexts/ChatMessageContext";
import ChatComponent from "./chat-component/ChatComponent";
import TextGenerationComponent from "./text-generation/TextGeneration";

const InteractionComponent = () => {
  const { messages } = useChatMessageContext();
  const chatInputs = messages?.team.team_inputs;
  const isChatInput = chatInputs?.some(
    (input) => input?.component?.provider === "autogen_core.io.ChatInput"
  );

  return (
    <div className="w-full flex justify-center items-center bg-[#F5F5F7]">
      {isChatInput ? <ChatComponent/> : <>{!!messages &&<TextGenerationComponent/> }</>}
    </div>
  );
};

export default InteractionComponent;
