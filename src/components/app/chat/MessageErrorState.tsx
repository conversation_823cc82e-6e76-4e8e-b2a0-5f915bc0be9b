import { Button } from "@/components/ui/button";

interface MessageErrorStateProps {
  onRetry: () => void;
  errorMessage?: string;
}

const MessageErrorState = ({ onRetry, errorMessage }: MessageErrorStateProps) => {
  return (
    <div className="flex h-full w-full flex-1 min-w-0 min-h-0 overflow-hidden">
      <div className="h-full flex justify-center items-center w-full">
        <div className="flex flex-col items-center gap-3">
          <div className="text-red-600 text-xl font-semibold mb-3">
            {errorMessage || "Error loading messages."}
          </div>
          <Button
            onClick={onRetry}
            variant="outline"
            className="h-auto rounded-lg font-semibold text-sm"
          >
            Try Again
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MessageErrorState;
