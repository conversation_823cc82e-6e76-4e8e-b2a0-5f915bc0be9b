import React from "react";
import { <PERSON><PERSON><PERSON>Close, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

interface SidebarToggleProps {
  onClick: () => void;
}

const SidebarToggle: React.FC<SidebarToggleProps> = ({ onClick }) => {
  return (
    <div
      className={
        "fixed top-12 right-0 h-full bg-white shadow-lg transform transition-all duration-300 ease-in-out z-40 w-20"
      }
    >
      {/* Header */}
      <div className="flex items-center justify-between pt-8 p-4 border-b border-gray-200">
        <PanelRightOpen
          className="h-[30px] w-[30px] cursor-pointer"
          onClick={onClick}
        />
      </div>
    </div>
  );
};

export default SidebarToggle;
