import { useState } from "react";
import { useChatMessageContext } from "@/contexts/ChatMessageContext";
import ChatInput from "./ChatInput";
import EmptyState from "./EmptyState";
import MessagesList from "./MessagesList";
import RightSidebar from "./RightSidebar";
import SidebarToggle from "../SidebarToggle";


const ChatComponent = () => {
  const { messages, inputConfig } = useChatMessageContext();
  const listMessages = messages?.messages;
  const teams = messages?.team;
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const handleToggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleCloseSidebar = () => {
    setIsSidebarOpen(false);
  };

  



  return (
    <div className="flex w-full">
      {/* Main Content Area */}
      <div
        className={`flex-1 transition-all duration-300 ease-in-out ${
          isSidebarOpen ? "mr-80" : "mr-0"
        }`}
      >
        <div className="w-full flex flex-col justify-center items-center pb-20">
          {listMessages.length === 0 ? (
            <EmptyState teams={teams} />
          ) : (
            <MessagesList listMessages={listMessages} teams={teams} />
          )}

          <ChatInput />
        </div>

        {/* Sidebar Toggle Button */}
        <SidebarToggle onClick={handleToggleSidebar} />
      </div>

      {/* Right Sidebar */}
      <RightSidebar isOpen={isSidebarOpen} onClose={handleCloseSidebar}/>
      
     
    </div>
  );
};

export default ChatComponent;
