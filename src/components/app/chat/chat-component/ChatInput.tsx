import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useSidebar } from "@/components/ui/sidebar";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { ArrowUp, Plus, Send } from "lucide-react";
import { useRef, useState } from "react";
import GenerateButton from "../../home/<USER>/generate-button";
import { useChatMessageContext } from "@/contexts/ChatMessageContext";

interface ChatInputProps {
  onSendMessage?: (message: string) => void;
}

const ChatInput = ({ onSendMessage }: ChatInputProps) => {
  const [inputMessage, setInputMessage] = useState("");

  const formRef = useRef<HTMLFormElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { handleSendMessage: handleChatMessage , messages, isSendingMessage , errorSendingMessage , inputConfig} = useChatMessageContext();

  function validateComponents(components): boolean {
    if (!Array.isArray(components)) {
      console.warn("validateComponents received invalid input:", components);
      return false;
    }
    for (const item of components) {
      const { provider, content, is_required } = item;
  
      if (provider === "autogen_core.io.ChatInput") {
        continue; 
      }

      
  
      if (is_required && 
        (typeof content === "string" && content.trim() === "")
      ) {
        return false;
      }
    }
  
    return true; 
  }

  const isMessageDisabled = isSendingMessage || !!errorSendingMessage || !validateComponents(inputConfig);

  const handleSendMessage = async(e) => {
    e.preventDefault();
    if (inputMessage.trim()) {
      // console.log("Sending message:", inputMessage);
      await handleChatMessage(messages?.id, inputMessage);
      setInputMessage("");
      textareaRef.current.value = "";
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  const handleInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
    setInputMessage(e.currentTarget.value);
  };

  return (
    <form
      ref={formRef}
      onSubmit={handleSendMessage}
      className="bg-white w-1/2 fixed bottom-4 shadow-lg p-4 rounded-3xl flex flex-col gap-3"
    >
      {/* Textarea */}
      <Label className="sr-only" htmlFor="user-input">
        User Prompt
      </Label>
      <Textarea
        autoFocus
        onKeyDown={handleKeyPress}
        ref={textareaRef}
        onChange={handleInput}
        className={cn(
          "border-0 ring-0 outline-0 focus-visible:border-0 focus-visible:ring-0 focus-visible:outline-0 focus-within:border-0 focus-within:ring-0 focus-within:outline-0 shadow-none",
          "resize-none rounded-sm min-h-[40px] max-h-[200px] "
          // !hasContent ? "scrollbar-hidden" : "overflow-y-auto"
        )}
        name="user-input"
        id="user-input"
        placeholder="Ask me anything"
        disabled={isMessageDisabled}
      />

      <div className="flex justify-end px-2">
        <GenerateButton
          className={cn(
            "rounded-full bg-teal-800 hover:bg-teal-800/80 hover:text-white text-white",
            "cursor-pointer w-8 h-8"
          )}
          disabled={isMessageDisabled}
          formRef={formRef}
          label="Chat"
          loadingLabel="Loading"
        />
      </div>
    </form>
  );
};

export default ChatInput;
