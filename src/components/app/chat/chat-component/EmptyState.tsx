interface EmptyStateProps {
  teams: any;
}

const EmptyState = ({ teams }: EmptyStateProps) => {
  return (
    <div className="flex flex-col gap-3 items-center mt-10">
      <h2 className="text-2xl font-bold">{teams?.component.label}</h2>
      <div className="w-96 bg-[#6AC3BD26] border-[#115E5926] border rounded-lg p-4">
        <p className="text-[#444444] whitespace-pre-wrap break-words line-clamp-3 overflow-hidden">{teams?.component.description}</p>
      </div>
    </div>
  );
};

export default EmptyState;
