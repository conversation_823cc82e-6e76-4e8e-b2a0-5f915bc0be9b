import { Card, CardContent } from "@/components/ui/card";
import Sparkle from "@/components/icons/sparkle";
import { useChatMessageContext } from "@/contexts/ChatMessageContext";
import CardSkeleton from "@/components/app/chat/CardSkeleton";
import { useEffect } from "react";
import Markdown from "../../markdown";

interface MessagesListProps {
  listMessages: any[];
  teams: any;
}

const MessagesList = ({ listMessages, teams }: MessagesListProps) => {
  const { isSendingMessage, scrollRef, scrollToBottom, isMessagesLoading, errorSendingMessage } =
    useChatMessageContext();

  function getChatInputContent(message: any) {
    if (!message?.config?.content || !Array.isArray(message.config.content)) {
      return null;
    }

    const chatInputItem = message.config.content.find(
      (item: any) => item.type === "ChatInput"
    );
    return chatInputItem?.content || null;
  }

  useEffect(() => {
    scrollToBottom();
  }, [isMessagesLoading]);

  return (
    <div
      className="w-3/5 2xl:px-5 xl:px-4 md:px-3 space-y-4 max-h-[70vh] overflow-y-scroll scrollbar-hide pb-20"
      ref={scrollRef}
    >
      {listMessages.map((message) => (
        <div
          key={message.id}
          className={`flex ${
            message.config.source === "user" ? "justify-end" : "justify-start"
          }`}
        >
          <div
            className={`flex items-start gap-3 max-w-2xl ${
              message.config.source === "user" ? "flex-row-reverse" : "flex-row"
            }`}
          >
            <Card
              className={`${
                message.config.source === "user"
                  ? "bg-teal-800 text-white rounded-[20px] mt-6"
                  : "bg-white rounded-[20px]"
              }`}
            >
              {message.config.source === "user" ? (
                <CardContent className="p-3">
                  <p className="text-sm whitespace-pre-wrap">
                    {getChatInputContent(message)}
                  </p>
                  <div className="text-xs mt-2 text-teal-100"></div>
                </CardContent>
              ) : (
                <CardContent className="p-5 px-8 space-y-4">
                  <div className="flex justify-start space-x-2 items-center">
                    <div className="w-8 h-8 rounded-full flex items-center justify-center bg-teal-800 text-white">
                      <Sparkle className="h-4 w-4" />
                    </div>
                    <h4 className="text-sm font-semibold leading-[130%]">
                      {teams?.component.label}
                    </h4>
                  </div>
                  <Markdown className="text-sm whitespace-pre-wrap">{message.config.content}</Markdown>
                  <div className="text-xs mt-2 text-teal-100"></div>
                </CardContent>
              )}
            </Card>
          </div>
        </div>
      ))}

      {isSendingMessage && <CardSkeleton teamLabel={teams?.component.label} />}

      {!!errorSendingMessage && (
        <div className="text-red-500 text-center">
          Error sending message: {errorSendingMessage}. Please try again.
        </div>
      )}
    </div>
  );
};

export default MessagesList;
