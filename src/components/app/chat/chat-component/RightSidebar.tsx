import React, { useEffect } from "react";
import { CloudCog, PanelRightClose, X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useChatMessageContext } from "@/contexts/ChatMessageContext";
import FileUploader from "../commonComponent/FileUploader";
import TextInput from "../commonComponent/TextInput";

interface RightSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const RightSidebar: React.FC<RightSidebarProps> = ({ isOpen, onClose }) => {
  const { messages, setInputConfig, inputConfig } = useChatMessageContext();

  function getFilenameFromUrl(url: string): string {
    try {
      const parsedUrl = new URL(url);
      const pathname = parsedUrl.pathname;
      const fileName = pathname.substring(pathname.lastIndexOf('/') + 1);
      return decodeURIComponent(fileName);
    } catch (error) {
      console.error("Invalid URL:", error);
      return '';
    }
  }

  useEffect(() => {
    if (!messages) return;
    if (messages.messages.length === 0) {
      const config = messages.team.team_inputs.map((i) => {
        switch (i.component.provider) {
          case "autogen_core.io.FileInput":
            return {
              id: i.id,
              provider: i.component.provider,
              type: "FileInput",
              file_type: i.component.config.file_type,
              content: i.component.config.content || "",
              label: i.component.label || "File Input",
              is_required: i.component.config.required,
            };
          case "autogen_core.io.TextInput":
            return {
              id: i.id,
              provider: i.component.provider,
              type: "TextInput",
              content: i.component.config.content || "",
              label: i.component.label || "Text Input",
              is_required: i.component.config.required,
            };
          case "autogen_core.io.URLInput":
            return {
              id: i.id,
              provider: i.component.provider,
              type: "URLInput",
              content: {
                url: i.component.config.url,
                headers: i.component.config.headers,
              },
              label: i.component.label || "URL Input",
              is_required: i.component.config.required,
            };
          case "autogen_core.io.ImageInput":
            return {
              id: i.id,
              provider: i.component.provider,
              type: "ImageInput",
              content: i.component.config.content,
              label: "Image Input",
              is_required: i.component.config.required,
            };
          case "autogen_core.io.ChatInput":
            return {
              id: i.id,
              provider: i.component.provider,
              type: "ChatInput",
              content: "",
              label: "Chat Input",
              is_required: i.component.config.required,
            };
          default:
            console.warn("DEBUG: Unknown input type:", i.component);
            return {
              id: i.id,
              provider: i.component.provider || "unknown",
              type: "TextInput",
              content: "unknown",
              label: "Unknown Input",
              is_required: false,
            };
        }
      });

      setInputConfig(config);
    } else {
      const config = messages.messages[0].config.content;
      setInputConfig(config);
    }
  }, [messages?.id]);

  console.log("Setting input config:", inputConfig);

  const isDisable = messages.messages.length > 0;

  const handleSetInputConfig = (input_id: string, key: string, value: any) => {
    setInputConfig(
      inputConfig.map((input) => {
        if (input.id === input_id) {
          return {
            ...input,
            [key]: value,
          };
        }
        return input;
      })
    );
  };

  const handleSetUrlInputConfig = (
    input_id: string,
    key: string,
    value: any
  ) => {
    setInputConfig(
      inputConfig.map((input) => {
        if (input.id === input_id) {
          return {
            ...input,
            content: {
              ...input.content,
              [key]: value,
            },
          };
        }
        return input;
      })
    );
  };

  return (
    <div
      className={`fixed top-12 right-0 h-full bg-white shadow-lg transform transition-all duration-300 ease-in-out z-40 ${
        isOpen ? "w-80 translate-x-0" : "w-0 translate-x-full"
      }`}
    >
      <div className="flex items-center justify-between pt-8 p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <PanelRightClose
            className="h-[30px] w-[30px] cursor-pointer"
            onClick={onClose}
          />

          <h2 className="text-lg font-semibold text-gray-900">Add Inputs</h2>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0 hover:bg-gray-100"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="p-4 pb-96 h-full overflow-y-auto flex flex-col gap-5">
        {inputConfig?.map((input) => {
          if (
            input.provider === "autogen_core.io.FileInput" ||
            input.provider === "autogen_core.io.ImageInput" || input.type === "FileInput" || input.type === "ImageInput"
          ) {
            return (
              <div key={input.id} className="mb-6">
                <FileUploader
                  label={input.label}
                  onUpload={(objectName: string) =>
                    handleSetInputConfig(input.id, "content", objectName)
                  }
                  uploadedFileName={input.content ? getFilenameFromUrl(input.content) : null}
                  input_id={input.id}
                  isDisabled={isDisable}
                />
              </div>
            );
          } else if (input.provider === "autogen_core.io.TextInput" || input.type === "TextInput") {
            return (
              <div key={input.id} className="">
                <TextInput
                  label={input.label}
                  placeholder={`Enter ${input.label.toLowerCase()}...`}
                  value={input.content}
                  onChange={(value) =>
                    handleSetInputConfig(input.id, "content", value)
                  }
                  isRequired={input.is_required}
                  maxLength={2000}
                  rows={6}
                  isDisabled={isDisable}
                />
              </div>
            );
          } else if (input.provider === "autogen_core.io.URLInput" || input.type === "URLInput") {
            return (
              <div key={input.id} className="">
                <TextInput
                  label={input.label}
                  placeholder="Enter URL (e.g., https://example.com)"
                  value={input.content.url}
                  onChange={(value) =>
                    handleSetUrlInputConfig(input.id, "url", value)
                  }
                  isRequired={input.is_required}
                  rows={1}
                  isDisabled={isDisable}
                />

                <TextInput
                  label="Headers"
                  placeholder="{Authorization: 'Bearer token'}"
                  value={JSON.stringify(input.content.headers)}
                  onChange={(value) => {
                    handleSetUrlInputConfig(input.id, "headers", value);
                  }}
                  rows={3}
                  isDisabled={isDisable}
                />
              </div>
            );
          }
        })}
      </div>
    </div>
  );
};

export default RightSidebar;
