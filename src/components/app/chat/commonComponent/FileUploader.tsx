import { Upload, Eye } from "lucide-react";
import React, { useState } from "react";
import ChatService from "@/services/chatService";
import { useChatMessageContext } from "@/contexts/ChatMessageContext";
import FileDisplay from "@/components/FileDisplay";

interface FileUploaderProps {
  label?: string;
  acceptedTypes?: string;
  isLoading?: boolean;
  onUpload?: (objectName: string) => void;
  uploadedFileName?: string | null;
  placeholder?: string;
  isRequired?: boolean;
  input_id: string;
  isDisabled?: boolean;
}

const FileUploader: React.FC<FileUploaderProps> = ({
  label = "Upload File",
  acceptedTypes = "*",
  isLoading,
  onUpload,
  uploadedFileName = null,
  placeholder = "Click or drop a file",
  isRequired = false,
  input_id,
  isDisabled = false,
}) => {
  const [localFileName, setLocalFileName] = useState<string | null>(null);
  const [localFileType, setLocalFileType] = useState<string | null>(null);
  const [presignedUrl, setPresignedUrl] = useState<string | null>(null);
  const [internalLoading, setInternalLoading] = useState(false);
  const { messages } = useChatMessageContext();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setLocalFileName(null); // Clear current
    setLocalFileType(null); // Clear current file type
    setPresignedUrl(null); // Clear current presigned URL
    setInternalLoading(true);

    try {
      // Generate presigned URL
      const presignedResponse = await ChatService.generatePresignedURL({
        object_name: `agentic-platform/${messages?.id}/${input_id}/${file.name}`,
        content_type: file.type || "application/octet-stream",
        expiration_minutes: 15,
      });

      // Upload file to presigned URL
      await ChatService.uploadFileToPresignedURL(
        presignedResponse.presigned_url,
        file,
        file.type || "application/octet-stream"
      );

      // Call the onUpload prop to notify parent

      setLocalFileName(file.name); // Update local display
      setLocalFileType(file.type || "application/octet-stream"); // Store file type
      setPresignedUrl(presignedResponse.presigned_url); // Store presigned URL
      if (onUpload) {
        const presignedUrl = presignedResponse.presigned_url;
        const url = presignedUrl.split("?")[0];
        onUpload(url);
        console.log("File uploaded successfully:", url);
      }

      //     fileName: file.name,
      //     provider: presignedResponse.provider,
      //     objectName: presignedResponse.object_name
      //   });
    } catch (error) {
      console.error("File upload failed:", error);
      setLocalFileName(null);
      setLocalFileType(null);
      setPresignedUrl(null);
      // You might want to show an error message to the user here
    } finally {
      setInternalLoading(false);
    }
  };

  const handleViewFile = () => {
    if (presignedUrl) {
      window.open(presignedUrl, '_blank');
    }
  };

  const fileNameToDisplay = uploadedFileName || localFileName;
  const showLoading = isLoading || internalLoading;

  return (
    <div className={`flex flex-col gap-4 w-full `}>
      <label className="font-semibold text-gray-600 text-sm">
        {label} {isRequired && <span className="text-red-500">*</span>}
      </label>

      <div className={`relative border border-gray-300 rounded-lg hover:bg-gray-50 transition ${isDisabled ? "opacity-50" : ""}`}>
        <div className="flex items-center">
          {/* File Upload Area */}
          <div className={`relative flex-1 p-6 text-center ${isDisabled ? "cursor-not-allowed" : "cursor-pointer"}`}>
            {showLoading ? (
              <div className="flex justify-center items-center gap-2 text-gray-500">
                <svg
                  className="animate-spin h-5 w-5 text-[#115E59]"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8v8z"
                  ></path>
                </svg>
                Uploading...
              </div>
            ) : (
              <>
                <input
                  type="file"
                  accept={acceptedTypes}
                  className={`absolute inset-0 opacity-0 ${isDisabled ? "cursor-not-allowed" : "cursor-pointer"}`}
                  onChange={handleFileChange}
                  disabled={isDisabled}
                />
                <div className="text-teal-700 font-medium">
                  {fileNameToDisplay ? (
                    <FileDisplay
                      fileName={fileNameToDisplay}
                      mimeType={localFileType || undefined}
                      className="text-teal-600"
                    />
                  ) : (
                    <div className="flex flex-col items-center text-sm">
                      <Upload className="h-5 w-5 text-[#115E59]" />
                      <span>{placeholder}</span>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>

          {/* View File Button */}
          {fileNameToDisplay && presignedUrl && !showLoading && (
            <div className="p-4">
              <button
                onClick={handleViewFile}
                className="p-2 text-black hover:text-teal-900 hover:bg-teal-100 rounded transition-colors"
                title="View file"
              >
                <Eye className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileUploader;
