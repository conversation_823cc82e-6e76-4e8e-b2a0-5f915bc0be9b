import React, { useState, useEffect } from "react";

interface TextInputProps {
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  maxLength?: number;
  rows?: number;
  isDisabled?: boolean;
}

const TextInput: React.FC<TextInputProps> = ({
  label = "Text Input",
  placeholder = "Enter your text here...",
  isRequired = false,
  value = "",
  onChange,
  maxLength,
  rows = 4,
  isDisabled = true,
}) => {
  const [localValue, setLocalValue] = useState(value || "");
  const [charCount, setCharCount] = useState((value || "").length);

  // Update local state when value prop changes
  useEffect(() => {
    if (localValue !== undefined) {
      setCharCount(localValue.length);
    }
  }, [localValue]);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= (maxLength || 2000)) {
      setLocalValue(newValue);
      setCharCount(newValue.length);
      if (onChange) {
        onChange(newValue);
      }
    }
  };

  const handleClear = () => {
    setLocalValue("");
    setCharCount(0);
    if (onChange) {
      onChange("");
    }
  };

  return (
    <div className="flex flex-col gap-4 w-full ">
      <label className="font-semibold text-gray-600 text-sm">
        {label} {isRequired && <span className="text-red-500">*</span>}
      </label>

      <div className="relative">
        <textarea
          value={localValue}
          onChange={handleTextChange}
          placeholder={placeholder}
          rows={rows}
          className="w-full resize-none border border-gray-300 rounded-md p-3 text-gray-700 placeholder-gray-400 text-sm focus:border-[#115E59] focus:ring-1 focus:ring-[#115E59] focus:outline-none"
          disabled={isDisabled}
        />

        <div className="flex justify-between items-center mt-2">
          {maxLength && (
            <span className="text-xs text-gray-500">
              {charCount}/{maxLength} characters
            </span>
          )}
          {maxLength &&localValue && (
            <button
              onClick={handleClear}
              className="text-xs text-gray-500 hover:text-red-600 transition-colors"
            >
              Clear
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default TextInput;
