import { useEffect, useState } from "react";
import { useChatMessageContext } from "@/contexts/ChatMessageContext";
import { Card } from "@/components/ui/card";
import { <PERSON>rk<PERSON>, Wand } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import FileUploader from "../commonComponent/FileUploader";
import TextInput from "../commonComponent/TextInput";
import Markdown from "../../markdown";
import StatusBadge from "./StatusBadge";
import LoadingScreen from "./LoadingScreen";

const OutputCard = () => {
  const { messages, isSendingMessage } = useChatMessageContext();
  const output = messages?.messages[1]?.config.content || null;
  const isFailed = messages?.messages.length === 1;

  return (
    <>
      {(isSendingMessage || isFailed || output) && (
        <Card className="flex flex-col gap-5">
          <div className="flex justify-between items-center border-b border-b-gray-200 p-4">
            <div className="flex gap-1 items-center">
              <Wand className="h-6 w-6" />
              <h2 className="text-xl -tracking-[1%] leading-7 font-semibold">
                Output
              </h2>
            </div>
            {isSendingMessage ? (
              <StatusBadge
                color="border-blue-500 text-blue-500"
                displayName="Processing"
              />
            ) : isFailed ? (
              <StatusBadge
                color="border-red-500 text-red-500"
                displayName="Failed"
              />
            ) : (
              <StatusBadge
                color="border-green-500 text-green-500"
                displayName="Generated"
              />
            )}
          </div>
          {isSendingMessage ? (
            <div className="p-4  h-full overflow-y-auto flex flex-col gap-5">
              <LoadingScreen />
            </div>
          ) : isFailed ? (
            <div className="p-4  h-full flex flex-col gap-5">
              <h1 className="text-red-500">Failed to generate output. Please create a new session and Try Again</h1>
            </div>
          ) : (
            <div className="p-4  h-full overflow-y-auto flex flex-col gap-5">
              {output && <Markdown>{output}</Markdown>}
            </div>
          )}
        </Card>
      )}
    </>
  );
};

export default OutputCard;
