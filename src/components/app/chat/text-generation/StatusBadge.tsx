import { Badge } from "@/components/ui/badge";
import { useChatMessageContext } from "@/contexts/ChatMessageContext";

const StatusBadge = ({
  color,
  displayName,
}) => {
  const { messages, isSendingMessage } = useChatMessageContext();
  const output = messages?.messages[1]?.config.content || null;

  return (
    <Badge variant="outline" className={`${color} px-4 py-1`}>
     {displayName}
    </Badge>
  );
};

export default StatusBadge;
