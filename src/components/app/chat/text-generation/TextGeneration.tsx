import { useState } from "react";
import { useChatMessageContext } from "@/contexts/ChatMessageContext";
import TextInputCard from "./TextInputCard";
import OutputCard from "./OutputCard";

const TextGenerationComponent = () => {
  const { messages, inputConfig, isSendingMessage } = useChatMessageContext();
  const teams = messages?.team;
  const output = messages?.messages[1]?.config.content || null;
 
  return (
    <div className="flex w-full h-full">
      <div className="flex flex-col gap-6 h-full w-full px-20 py-10 ">
        <div className="flex flex-col gap-3 ">
          <h1 className="text-2xl font-semibold">{teams?.component.label}</h1>
          <h2 className="text-base font-medium text-[#444444] whitespace-pre-wrap break-words line-clamp-3">
            {teams?.component.description}
          </h2>
        </div>

        <TextInputCard />
        <OutputCard/>
      </div>
    </div>
  );
};

export default TextGenerationComponent;
