import { useEffect, useState } from "react";
import { useChatMessageContext } from "@/contexts/ChatMessageContext";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Wan<PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import FileUploader from "../commonComponent/FileUploader";
import TextInput from "../commonComponent/TextInput";

const TextInputCard = () => {
  const { messages, setInputConfig, inputConfig, handleSendMessage , isSendingMessage} =
    useChatMessageContext();
  const [isDisable, setIsDisable] = useState(false);
  function getFilenameFromUrl(url: string): string {
    try {
      const parsedUrl = new URL(url);
      const pathname = parsedUrl.pathname;
      const fileName = pathname.substring(pathname.lastIndexOf("/") + 1);
      return decodeURIComponent(fileName);
    } catch (error) {
      console.error("Invalid URL:", error);
      return "";
    }
  }
  const isAlreadyGenerated = messages.messages.length > 0;

  useEffect(() => {
    if (messages.messages.length === 0) {
      const config = messages.team.team_inputs.map((i) => {
        switch (i.component.provider) {
          case "autogen_core.io.FileInput":
            return {
              id: i.id,
              provider: i.component.provider,
              type: "FileInput",
              file_type: i.component.config.file_type,
              content: i.component.config.content || "",
              label: i.component.label || "File Input",
              is_required: i.component.config.required,
            };
          case "autogen_core.io.TextInput":
            return {
              id: i.id,
              provider: i.component.provider,
              type: "TextInput",
              content: i.component.config.content || "",
              label: i.component.label || "Text Input",
              is_required: i.component.config.required,
            };
          case "autogen_core.io.URLInput":
            return {
              id: i.id,
              provider: i.component.provider,
              type: "URLInput",
              content: {
                url: i.component.config.url,
                headers: i.component.config.headers,
              },
              label: i.component.label || "URL Input",
              is_required: i.component.config.required,
            };
          case "autogen_core.io.ImageInput":
            return {
              id: i.id,
              provider: i.component.provider,
              type: "ImageInput",
              content: i.component.config.content,
              label: "Image Input",
              is_required: i.component.config.required,
            };
          case "autogen_core.io.ChatInput":
            return {
              id: i.id,
              provider: i.component.provider,
              type: "ChatInput",
              content: "",
              label: "Chat Input",
              is_required: i.component.config.required,
            };
          default:
            console.warn("DEBUG: Unknown input type:", i.component);
            return {
              id: i.id,
              provider: i.component.provider || "unknown",
              type: "TextInput",
              content: "unknown",
              label: "Unknown Input",
              is_required: false,
            };
        }
      });

      setInputConfig(config);
    } else {
      const config = messages.messages[0].config.content;
      setInputConfig(config);
    }
  }, []);




  const handleSetInputConfig = (input_id: string, key: string, value: any) => {
    setInputConfig((prevInputConfig) =>
      prevInputConfig.map((input) => {
        if (input.id === input_id) {
          return {
            ...input,
            [key]: value,
          };
        }
        return input;
      })
    );
  };

  const handleSetUrlInputConfig = (
    input_id: string,
    key: string,
    value: any
  ) => {
    setInputConfig((prevInputConfig) =>
      prevInputConfig.map((input) => {
        if (input.id === input_id) {
          return {
            ...input,
            content: {
              ...input.content,
              [key]: value,
            },
          };
        }
        return input;
      })
    );
  };

  return (
    <Card className="flex flex-col gap-5">
      <div className="flex justify-between items-center border-b border-b-gray-200 p-4">
        <div className="flex gap-1 items-center">
          <Wand className="h-6 w-6" />
          <h2 className="text-xl -tracking-[1%] leading-7 font-semibold">
            Inputs
          </h2>
        </div>
        <Button
          className="flex gap-2 items-center bg-teal-800 hover:bg-teal-800/80 cursor-pointer"
          onClick={() => handleSendMessage(messages?.id, "")} // For Text generation no need to add message. It will get filtered out
          disabled={isSendingMessage || isAlreadyGenerated}
        >
          <Sparkle className="h-6 w-6" />
          <p>Generate</p>
        </Button>
      </div>

      <div className="p-4  h-full overflow-y-auto flex flex-col gap-5">
        {inputConfig?.map((input) => {
          if (
            input.provider === "autogen_core.io.FileInput" ||
            input.provider === "autogen_core.io.ImageInput" ||
            input.type === "FileInput" ||
            input.type === "ImageInput"
          ) {
            return (
              <div key={input.id} className="mb-6">
                <FileUploader
                  label={input.label}
                  onUpload={(objectName: string) => {
                    console.log("File uploaded:", objectName);
                    console.log("Input config:", input.id);
                    handleSetInputConfig(input.id, "content", objectName)
                  }
                  }
                  uploadedFileName={
                    input.content ? getFilenameFromUrl(input.content) : null
                  }
                  isRequired={input.is_required}
                  input_id={input.id}
                  isDisabled={isDisable || isAlreadyGenerated}
                />
              </div>
            );
          } else if (
            input.provider === "autogen_core.io.TextInput" ||
            input.type === "TextInput"
          ) {
            return (
              <div key={input.id} className="">
                <TextInput
                  label={input.label}
                  placeholder={`Enter ${input.label.toLowerCase()}...`}
                  value={input.content}
                  onChange={(value) =>
                    handleSetInputConfig(input.id, "content", value)
                  }
                  isRequired={input.is_required}
                  maxLength={2000}
                  rows={6}
                  isDisabled={isDisable || isAlreadyGenerated}
                />
              </div>
            );
          } else if (
            input.provider === "autogen_core.io.URLInput" ||
            input.type === "URLInput"
          ) {
            return (
              <div>
                <label className="font-semibold text-gray-600 text-sm">
                  {input.label}{" "}
                  {input.is_required && <span className="text-red-500">*</span>}
                </label>
                <div key={input.id} className=" flex gap-5">
                  <TextInput
                    label="URL"
                    placeholder="Enter URL (e.g., https://example.com)"
                    value={input.content.url}
                    onChange={(value) =>
                      handleSetUrlInputConfig(input.id, "url", value)
                    }
                    isRequired={input.is_required}
                    rows={1}
                    isDisabled={isDisable || isAlreadyGenerated}
                  />

                  <TextInput
                    label="Headers"
                    placeholder="{Authorization: 'Bearer token'}"
                    value={JSON.stringify(input.content.headers)}
                    onChange={(value) => {
                      handleSetUrlInputConfig(input.id, "headers", value);
                    }}
                    rows={3}
                    isDisabled={isDisable || isAlreadyGenerated}
                  />
                </div>
              </div>
            );
          }
        })}
      </div>
    </Card>
  );
};

export default TextInputCard;
