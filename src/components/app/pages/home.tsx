import { Outlet, useNavigate, useParams } from "react-router";
import Header from "@/components/app/header";
import Sidebar from "@/components/app/sidebar";
import { useAppStore } from "@/lib/app-store";
import { useTeam } from "@/hooks/useTeams";
import { useEffect, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { get_client_env } from "@/lib/env";
import { useSession } from "@/hooks/useSession";
import { useChatContext } from "@/contexts/ChatContext";

export default function HomeRoute() {
  const { fetchSessionsByTeam } = useChatContext();
  const { teamId } = useParams();

  const { sessions, isSessionFetched } = useChatContext();
  const navigate = useNavigate();

  useEffect(() => {
    if (teamId) {
      fetchSessionsByTeam(teamId);
    }
  }, [teamId]);

  useEffect(() => {
    if (sessions && sessions.items.length > 0 && isSessionFetched) {
      navigate(`/workflows/${teamId}/chat/sessions/${sessions.items[0].id}`);
    }
  }, [isSessionFetched]);

  return (
    <div className="flex flex-col h-screen w-screen">
      <Header />
      <div className="flex justify-between min-h-0 h-full">
        <Sidebar />
        <Outlet />
      </div>
    </div>
  );
}
