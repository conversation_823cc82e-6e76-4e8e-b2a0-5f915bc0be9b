import ChatInterface from "@/components/app/home/<USER>";
import { get_client_env } from "@/lib/env";
import axios from "axios";
import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { useAppStore } from "@/lib/app-store";
import { useChatMessageContext } from "@/contexts/ChatMessageContext";
import MessageLoadingSkeleton from "@/components/app/chat/MessageLoadingSkeleton";
import MessageErrorState from "@/components/app/chat/MessageErrorState";
import InteractionComponent from "../chat/InteractionComponent";

const getHeaders = () => {
  const token_type = localStorage.getItem("token_type");
  const access_token = localStorage.getItem("access_token");

  return { Authorization: `${token_type} ${access_token}` };
};

export default function Interface() {
  const { fetchMessagesBySession, isMessagesLoading, isMessageError } =
    useChatMessageContext();

  const { conversationId } = useParams();
  useEffect(() => {
    if (conversationId) {
      fetchMessagesBySession(conversationId);
    }
  }, [conversationId]);

  return (
    // store.selectedApp && (
    //   <div className="relative flex flex-1 min-w-0 min-h-0 overflow-hidden">
    //     <div className="flex-1 bg-neutral-100 overflow-auto">
    //       {!isLoading && <ChatInterface />}

    //       {/* {loaderData.component_type === "FileInput" && (
    //         <TextGeneration
    //           onFilesChange={handleFilesChange}
    //           onGenerate={handleGenerate}
    //           maxFiles={10}
    //         />
    //       )} */}
    //     </div>
    //     {/* Inputs sheet
    //       // {showInputSheet && (
    //       //   <InputsSheet collapse={collapse} setCollapse={setCollapse} />
    //       // )} */}
    //   </div>

    <>
      {isMessagesLoading ? (
        <MessageLoadingSkeleton />
      ) : !!isMessageError ? (
        <MessageErrorState
          onRetry={() => fetchMessagesBySession(conversationId!)}
          errorMessage={isMessageError}
        />
      ) : (
        <InteractionComponent />
      )}
    </>
  );
}
