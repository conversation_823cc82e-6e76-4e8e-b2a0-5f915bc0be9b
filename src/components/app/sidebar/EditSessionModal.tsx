import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  Di<PERSON><PERSON>itle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useChatContext } from "@/contexts/ChatContext";

interface EditSessionModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId: string;
  currentName: string;
}

export default function EditSessionModal({
  isOpen,
  onClose,
  sessionId,
  currentName,
}: EditSessionModalProps) {
  const [editName, setEditName] = useState(currentName);
  const { updateSession } = useChatContext();

  const handleSave = async () => {
    if (editName.trim() && editName !== currentName) {
      await updateSession(sessionId, editName.trim());
    }
    onClose();
  };

  const handleClose = () => {
    setEditName(currentName);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Session Name</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <Input
            value={editName}
            onChange={(e) => setEditName(e.target.value)}
            placeholder="Enter session name"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSave();
              }
            }}
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Update
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
