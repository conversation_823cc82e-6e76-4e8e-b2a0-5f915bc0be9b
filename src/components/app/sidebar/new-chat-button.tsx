import { Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useNavigate, useParams } from "react-router";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import axios from "axios";
import { get_client_env } from "@/lib/env";
import { useAppStore } from "@/lib/app-store";
import { Dispatch, SetStateAction } from "react";
import { useChatContext } from "@/contexts/ChatContext";

type Props = {
  app_type: ComponentType;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
};

export default function NewChat({ app_type, setIsOpen }: Props) {
  const navigate = useNavigate();
  const { createSession } = useChatContext();
  const { teamId } = useParams();

  const onSubmit = async (e) => {
    e.preventDefault();
    if (!teamId) return;
    try {
      const newSession = await createSession(teamId);

      navigate(`/workflows/${teamId}/chat/sessions/${newSession.id}`);
    } catch (error) {
      // console.error("Failed to close dialog:", error);
    }
  };

  return (
    <>
      <Button
        type="button"
        variant={"outline"}
        className={cn(
          "border border-teal-800 text-teal-800 transition-all",
          "hover:bg-teal-700 hover:text-white hover:ring hover:ring-teal-800",
          "font-medium text-sm",
          "cursor-pointer w-full py-4.5 rounded-md flex items-center gap-1.5 justify-center"
        )}
        onClick={onSubmit}
      >
        <Plus className="border-2 border-teal-800 rounded-full" />
        <span>
          {app_type === "TextInput" ? "New Conversation" : "New Generation"}
        </span>
      </Button>
    </>
  );
}
