import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON>, Trash, Edit } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { NavLink, useNavigate, useParams } from "react-router-dom";
import { ChatItem } from "@/types/chat";
import { useState, useEffect } from "react";
import EditSessionModal from "./EditSessionModal";
import DeleteSessionModal from "./DeleteSessionModal";

type Props = {
  app: ChatItem;
};

export default function SidebarItem({ app }: Props) {
  const navigate = useNavigate();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const { conversationId } = useParams();
  const isSelected = conversationId === app.id;

  const onSelectConversation = () => {
    navigate(`/workflows/${app.team?.id}/chat/sessions/${app.id}`);
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDeleteDialogOpen(true);
  };

  const handleEditClose = () => {
    setIsEditDialogOpen(false);
    // Force hover state reset after a small delay to ensure modal is fully closed
    setTimeout(() => {
      setIsHovered(false);
    }, 100);
  };

  const handleDeleteClose = () => {
    setIsDeleteDialogOpen(false);
    // Force hover state reset after a small delay to ensure modal is fully closed
    setTimeout(() => {
      setIsHovered(false);
    }, 100);
  };

  // Reset hover state when modals close
  useEffect(() => {
    if (!isEditDialogOpen && !isDeleteDialogOpen) {
      setIsHovered(false);
    }
  }, [isEditDialogOpen, isDeleteDialogOpen]);

  return (
    <>
      <div
        key={`session-${app.id}-${isEditDialogOpen}-${isDeleteDialogOpen}`}
        onClick={onSelectConversation}
        onMouseEnter={() => {
          console.log('Mouse enter on session:', app.name);
          setIsHovered(true);
        }}
        onMouseLeave={() => {
          console.log('Mouse leave on session:', app.name);
          setIsHovered(false);
        }}
        className={cn(
          "relative flex gap-2 items-center justify-between",
          "border border-transparent max-h-12",
          "p-3 rounded-lg transition-colors cursor-pointer text-sm font-medium",
          isSelected
            ? "bg-teal-800/10 text-teal-800 font-semibold"
            : "hover:bg-teal-800/15"
        )}
        title={app.name}
      >
        <div className="flex gap-2 items-center truncate">
          <div className="truncate">{app.name}</div>
        </div>

        <div
          className={cn(
            "opacity-0 transition-opacity",
            isHovered && "opacity-100"
          )}
          style={{
            opacity: isHovered ? 1 : 0,
            pointerEvents: isHovered ? 'auto' : 'none'
          }}
        >
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-slate-100"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
              >
                <Ellipsis className="h-4 w-4 text-gray-400" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              onClick={(e) => e.stopPropagation()}
            >
              <DropdownMenuItem onClick={handleEditClick}>
                <Edit className="h-4 w-4 mr-2" />
                Update name
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-red-600"
                onClick={handleDeleteClick}
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <EditSessionModal
        isOpen={isEditDialogOpen}
        onClose={handleEditClose}
        sessionId={app.id}
        currentName={app.name}
      />

      <DeleteSessionModal
        isOpen={isDeleteDialogOpen}
        onClose={handleDeleteClose}
        sessionId={app.id}
        sessionName={app.name}
      />
    </>
  );
}
