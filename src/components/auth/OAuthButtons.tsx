import React, { useContext } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { FeatureFlagContext } from "@/contexts/featureFlagContext";

export const OAuthButtons: React.FC = () => {
  const { initiateOAuth } = useAuth();
  const { toast } = useToast();
  const location = useLocation();
  const { enableGoogleLogin, enableGitHubLogin } = useContext(FeatureFlagContext);

  const handleOAuthLogin = async (provider: "google" | "github") => {
    try {
      // Get authorization URL from backend
      const authUrl = await initiateOAuth(provider);

      // Store whether user started from login or register page
      const sourcePage = location.pathname === "/register" ? "register" : "login";
      localStorage.setItem("oauth_source_page", sourcePage);

      // Redirect to the authorization URL in the same window
      window.location.href = authUrl;
    } catch (error) {
      toast({
        title: "Authentication Failed",
        description: error instanceof Error ? error.message : `Failed to authenticate with ${provider}`,
        variant: "destructive",
      });
    }
  };

  // Don't render anything if both OAuth options are disabled
  if (!enableGoogleLogin && !enableGitHubLogin) {
    return null;
  }

  return (
    <div className="space-y-3">
      {enableGoogleLogin && (
        <Button
          type="button"
          variant="outline"
          className="w-full h-11 border-gray-300 hover:bg-gray-50 text-gray-700"
          onClick={() => handleOAuthLogin("google")}
        >
          <img src="/google.svg" alt="Google" className="h-5 w-5 mr-2" />
          Continue with Google
        </Button>
      )}

      {enableGitHubLogin && (
        <Button
          type="button"
          variant="outline"
          className="w-full h-11 border-gray-300 hover:bg-gray-50 text-gray-700"
          onClick={() => handleOAuthLogin("github")}
        >
          <img src="/github.svg" alt="Github" className="h-5 w-5 mr-2" />
          Continue with GitHub
        </Button>
      )}

      {(enableGoogleLogin || enableGitHubLogin) && (
        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white px-2 text-gray-500">Or</span>
          </div>
        </div>
      )}
    </div>
  );
};
