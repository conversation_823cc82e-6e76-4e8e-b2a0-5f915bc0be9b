import {
    <PERSON><PERSON>,
    DialogContent,
    <PERSON>alogDescription,
    <PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON>eader,
    <PERSON>alog<PERSON><PERSON><PERSON>,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import React from "react";
import { Eye, EyeOff, Plus } from "lucide-react";

export default function CreateEnvVarModal({
    formRef,
    handleSubmit,
    open,
    setOpen,
    children,
}: {
    formRef: React.RefObject<HTMLFormElement>;
    handleSubmit: (e: React.FormEvent) => void;
    open: boolean;
    setOpen: (open: boolean) => void;
    children: React.ReactNode;
}) {
    const [env_type, setEnvType] = React.useState<string>("string");
    const [showPassword, setShowPassword] = React.useState(false);

    const getPlaceholder = () => {
        switch (env_type) {
            case "string": return "text value";
            case "number": return "123";
            case "boolean": return "true or false";
            case "secret": return "sk-***";
            default: return "value";
        }
    };

    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="mb-4">
            <DialogTitle>Add Variable</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <form ref={formRef} onSubmit={handleSubmit}>
            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-2">
                <Label htmlFor="env-type">
                  <strong>Type</strong>
                </Label>
                <Select
                  name="env-type"
                  defaultValue="string"
                  value={env_type}
                  onValueChange={setEnvType}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent id="env-type">
                    <SelectItem value="string">String</SelectItem>
                    <SelectItem value="number">Number</SelectItem>
                    <SelectItem value="boolean">Boolean</SelectItem>
                    <SelectItem value="secret">Secret</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-col gap-2">
                <Label htmlFor="name">
                  <strong>Name</strong>
                </Label>
                <Input
                  aria-required
                  id="name"
                  name="name"
                  placeholder="X_API_KEY"
                />
              </div>
              <div className="flex flex-col gap-2">
                <Label htmlFor="value">
                  <strong>Value</strong>
                </Label>
                <div className="relative">
                  <Input
                    aria-required
                    id="value"
                    name="value"
                    type={
                      env_type === "secret" && !showPassword
                        ? "password"
                        : "text"
                    }
                    placeholder={getPlaceholder()}
                  />
                  {env_type === "secret" && (
                    <Button
                      type="button"
                      variant="ghost"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </form>
          <DialogFooter className="mt-6">
            <Button
              onClick={() => formRef.current?.requestSubmit()}
              type="submit"
              className="bg-teal-800 hover:bg-teal-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Variable
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
}
