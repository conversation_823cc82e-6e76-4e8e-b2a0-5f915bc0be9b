import React, { useState, useRef } from "react";
import {
    <PERSON>,
    CardH<PERSON>er,
    Card<PERSON><PERSON>le,
    CardContent,
    CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell,
} from "@/components/ui/table";
import { Plus, Trash, Key, Save } from "lucide-react";
import DeleteEnvVarModal from "./DeleteEnvVarModal";

import { EnvVarSkeleton, CreateEnvVarModal } from "@/components/env-var";
import { useEnvVars } from "@/contexts/EnvsContext";
import { useToast } from "@/hooks/use-toast";
import { Empty } from "../Empty";

const EnvVarManagment: React.FC = () => {
  const {
    envVars,
    isLoading,
    createEnvVar,
    deleteEnvVar,
    total,
    page,
    pages,
    setPage,
    search,
    setSearch,
  } = useEnvVars();

  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [selectedEnvVar, setSelectedEnvVar] = useState<any>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new FormData(e.target as HTMLFormElement);

    // check if already exists
    const name = formData.get("name") as string;
    const existingEnv = envVars.filter((env_var) => {
      if (env_var.name === name) {
        return true;
      }
      return false;
    });
    if (existingEnv.length > 0) {
      toast({
        title: "Duplicate",
        description: "An environment variable with this name already exists.",
        variant: "destructive",
      });
      return;
    }

    await createEnvVar({
      name: formData.get("name") as string,
      value: formData.get("value") as string,
      // @ts-ignore
      type: formData.get("env-type") as string,
    })
      .then((data) => {
        setModalOpen(false);
      })
      .catch((error) => {
        console.error("Failed to create environment variable:", error);
      });
  };

  if (typeof envVars === "undefined") return null;

  return (
    <div className="min-w-fit mx-auto p-6">
      <Card>
        <CardHeader className="max-w-fit flex flex-col gap-2">
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" /> <span>Environment Variables</span>
          </CardTitle>
          <CardDescription>
            Manage and configure your environment variables
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-start gap-3 items-center mb-6">
            <CreateEnvVarModal
              formRef={formRef}
              handleSubmit={handleSubmit}
              open={modalOpen}
              setOpen={setModalOpen}
            >
              <Button
                onClick={() => setModalOpen(true)}
                className="bg-teal-800 hover:bg-teal-700 text-white h-auto flex items-center font-medium transition-colors rounded-lg py-[10px] px-[16px] text-sm"
              >
                <Plus className="h-5 w-5" />
                <span>Add</span>
              </Button>
            </CreateEnvVarModal>
            <Button
              variant="outline"
              className="text-teal-800 h-auto flex items-center font-medium transition-colors rounded-lg py-[10px] px-[16px] text-sm"
            >
              <Save />
              <span>Save</span>
            </Button>
          </div>

          {isLoading ? (
            <EnvVarSkeleton />
          ) : (
            <>
              {envVars.length === 0 && (
                <Empty
                  title="No Environment Variables"
                  description="Create your first environment variable"
                />
              )}
              {envVars.length > 0 && (
                <div className="w-full rounded-lg border">
                  <Table className="w-full rounded-lg">
                    <TableHeader>
                      <TableRow>
                        <TableHead className="font-bold px-4 py-2 text-center">
                          <strong>Name</strong>
                        </TableHead>
                        <TableHead className="font-bold px-4 py-2 text-center">
                          <strong>Value</strong>
                        </TableHead>
                        <TableHead className="font-bold px-4 py-2 text-center">
                          <strong>Actions</strong>
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {envVars.map((secret) => (
                        <TableRow
                          key={secret.name}
                          className="hover:bg-accent transition"
                        >
                          <TableCell className="px-4 py-2 text-center">
                            {secret.name}
                          </TableCell>
                          <TableCell className="px-4 py-2 text-center">
                            {secret.value}
                          </TableCell>
                          <TableCell className="px-4 py-2 text-center">
                            <Button
                              variant="ghost"
                              onClick={() => {
                                setSelectedEnvVar(secret);
                                setDeleteOpen(true);
                              }}
                            >
                              <Trash className="h-4 w-4 text-red-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      <DeleteEnvVarModal
        isOpen={deleteOpen}
        setIsOpen={setDeleteOpen}
        envVar={selectedEnvVar}
        onDelete={() => selectedEnvVar && deleteEnvVar(selectedEnvVar.name)}
      />
    </div>
  );
};

export default EnvVarManagment;
