import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ChunkingConfigurationCardProps {
  chunkingType: string;
  chunkSize: string;
  chunkOverlap: string;
  onInputChange: (field: string, value: string) => void;
}

export const ChunkingConfigurationCard: React.FC<
  ChunkingConfigurationCardProps
> = ({ chunkingType, chunkSize, chunkOverlap, onInputChange }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Chunking Configuration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="chunkingType">Chunking Type</Label>
          <Select
            value={chunkingType}
            onValueChange={(value) => onInputChange("chunkingType", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select chunking type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="RecursiveCharacterTextSplitter">
                Recursive Character Text Splitter
              </SelectItem>
              {/* <SelectItem value="CharacterTextSplitter">Character Text Splitter</SelectItem> */}
            </SelectContent>
          </Select>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="chunkSize">Chunk Size</Label>
            <Input
              id="chunkSize"
              type="number"
              value={chunkSize}
              onChange={(e) => onInputChange("chunkSize", e.target.value)}
              placeholder="800"
            />
          </div>
          <div>
            <Label htmlFor="chunkOverlap">Chunk Overlap</Label>
            <Input
              id="chunkOverlap"
              type="number"
              value={chunkOverlap}
              onChange={(e) => onInputChange("chunkOverlap", e.target.value)}
              placeholder="400"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
