import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useCreateKnowledgeBase } from "@/hooks/useKnowledgeBase";
import { KnowledgeBase } from "@/types/knowledge-base";
import { BasicInformationCard, FileUploadCard, PageHeader } from "../shared";
import { EmbeddingConfigurationCard } from "./EmbeddingConfigurationCard";
import { ChunkingConfigurationCard } from "./ChunkingConfigurationCard";
import { VectorDatabaseConfigurationCard } from "./VectorDatabaseConfigurationCard";

interface CreateKnowledgeBasePageProps {
  onBack: () => void;
  knowledgeBase?: KnowledgeBase | null;
}

interface UploadedFile {
  file: File;
  id: string;
}

export const CreateKnowledgeBasePage: React.FC<
  CreateKnowledgeBasePageProps
> = ({ onBack, knowledgeBase }) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    // Chunking configuration
    chunkingType: "RecursiveCharacterTextSplitter",
    chunkSize: "800",
    chunkOverlap: "400",
    // Embedding configuration
    embeddingType: "OpenAIEmbeddings",
    embeddingModel: "text-embedding-3-small",
    // Vector database configuration
    useOwnWeaviate: false,
    weaviateUrl: "",
    weaviateApiKey: "",
    weaviateOpenAIApiKey: "",
    weaviateGrpcHost: "",
    weaviateGrpcPort: "443",
    weaviateHttpHost: "",
    weaviateHttpPort: "443",
    weaviateGrpcSecure: true,
    weaviateHttpSecure: true,
    weaviateIsEmbedded: false,
    weaviateCollectionName: "",
    weaviateTimeout: "30",
    weaviateEmbeddingProvider: "openai",
  });
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const createKnowledgeBase = useCreateKnowledgeBase();

  const isEditMode = !!knowledgeBase;

  // Initialize form when component mounts
  useEffect(() => {
    if (knowledgeBase) {
      // Parse embedding_config JSON string to get the model
      let embeddingType = "OpenAIEmbeddings";
      let embeddingModel = "text-embedding-3-small";
      try {
        const embeddingConfig = JSON.parse(
          knowledgeBase.embedding_config || "{}",
        );
        embeddingType = embeddingConfig.type || "OpenAIEmbeddings";
        embeddingModel =
          embeddingConfig.kwargs?.model || "text-embedding-3-small";
      } catch (error) {
        console.warn("Failed to parse embedding_config:", error);
      }

      // Parse chunking_config JSON string
      let chunkingType = "RecursiveCharacterTextSplitter";
      let chunkSize = "800";
      let chunkOverlap = "400";
      try {
        const chunkingConfig = JSON.parse(
          knowledgeBase.chunking_config || "{}",
        );
        chunkingType = chunkingConfig.type || "RecursiveCharacterTextSplitter";
        chunkSize = chunkingConfig.kwargs?.chunk_size?.toString() || "800";
        chunkOverlap =
          chunkingConfig.kwargs?.chunk_overlap?.toString() || "400";
      } catch (error) {
        console.warn("Failed to parse chunking_config:", error);
      }

      // Parse vector_db_config JSON string
      let useOwnWeaviate = false;
      let weaviateUrl = "";
      let weaviateApiKey = "";
      let weaviateOpenAIApiKey = "";
      let weaviateGrpcHost = "";
      let weaviateGrpcPort = "443";
      let weaviateHttpHost = "";
      let weaviateHttpPort = "443";
      let weaviateGrpcSecure = true;
      let weaviateHttpSecure = true;
      let weaviateIsEmbedded = false;
      let weaviateCollectionName = "";
      let weaviateTimeout = "30";
      let weaviateEmbeddingProvider = "openai";

      try {
        const vectorDbConfig = JSON.parse(
          knowledgeBase.vector_db_config || "{}",
        );
        if (Object.keys(vectorDbConfig).length > 0) {
          useOwnWeaviate = true;
          weaviateUrl = vectorDbConfig.url || "";
          weaviateApiKey = vectorDbConfig.api_key || "";
          weaviateOpenAIApiKey =
            vectorDbConfig.headers?.["X-Openai-Api-Key"] || "";
          weaviateGrpcHost = vectorDbConfig.grpc_host || "";
          weaviateGrpcPort = vectorDbConfig.grpc_port?.toString() || "443";
          weaviateHttpHost = vectorDbConfig.http_host || "";
          weaviateHttpPort = vectorDbConfig.http_port?.toString() || "443";
          weaviateGrpcSecure = vectorDbConfig.grpc_secure !== false;
          weaviateHttpSecure = vectorDbConfig.http_secure !== false;
          weaviateIsEmbedded = vectorDbConfig.is_embedded === true;
          weaviateCollectionName = vectorDbConfig.collection_name || "";
          weaviateTimeout =
            vectorDbConfig.additional_config?.timeout?.[0]?.toString() || "30";
          weaviateEmbeddingProvider =
            vectorDbConfig.embedding_provider || "openai";
        }
      } catch (error) {
        console.warn("Failed to parse vector_db_config:", error);
      }

      setFormData({
        name: knowledgeBase.name,
        description: knowledgeBase.description || "",
        chunkingType,
        chunkSize,
        chunkOverlap,
        embeddingType,
        embeddingModel,
        useOwnWeaviate,
        weaviateUrl,
        weaviateApiKey,
        weaviateOpenAIApiKey,
        weaviateGrpcHost,
        weaviateGrpcPort,
        weaviateHttpHost,
        weaviateHttpPort,
        weaviateGrpcSecure,
        weaviateHttpSecure,
        weaviateIsEmbedded,
        weaviateCollectionName,
        weaviateTimeout,
        weaviateEmbeddingProvider,
      });
    } else {
      setFormData({
        name: "",
        description: "",
        chunkingType: "RecursiveCharacterTextSplitter",
        chunkSize: "800",
        chunkOverlap: "400",
        embeddingType: "OpenAIEmbeddings",
        embeddingModel: "text-embedding-3-small",
        useOwnWeaviate: false,
        weaviateUrl: "",
        weaviateApiKey: "",
        weaviateOpenAIApiKey: "",
        weaviateGrpcHost: "",
        weaviateGrpcPort: "443",
        weaviateHttpHost: "",
        weaviateHttpPort: "443",
        weaviateGrpcSecure: true,
        weaviateHttpSecure: true,
        weaviateIsEmbedded: false,
        weaviateCollectionName: "",
        weaviateTimeout: "30",
        weaviateEmbeddingProvider: "openai",
      });
    }
    setUploadedFiles([]);
  }, [knowledgeBase]);

  const onDrop = (acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map((file) => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
    }));
    setUploadedFiles((prev) => [...prev, ...newFiles]);
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSwitchChange = (field: string, value: boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      return;
    }

    // Build vector_db_config based on toggle
    let vectorDbConfig = {};
    if (formData.useOwnWeaviate) {
      vectorDbConfig = {
        url: formData.weaviateUrl || null,
        api_key: formData.weaviateApiKey,
        headers: {
          "X-Openai-Api-Key": formData.weaviateOpenAIApiKey,
        },
        grpc_host: formData.weaviateGrpcHost,
        grpc_port: parseInt(formData.weaviateGrpcPort) || 443,
        http_host: formData.weaviateHttpHost,
        http_port: parseInt(formData.weaviateHttpPort) || 443,
        grpc_secure: formData.weaviateGrpcSecure,
        http_secure: formData.weaviateHttpSecure,
        is_embedded: formData.weaviateIsEmbedded,
        collection_name: formData.weaviateCollectionName,
        additional_config: {
          timeout: [
            parseInt(formData.weaviateTimeout) || 30,
            parseInt(formData.weaviateTimeout) || 30,
          ],
        },
        embedding_provider: formData.weaviateEmbeddingProvider,
      };
    }

    try {
      await createKnowledgeBase.mutateAsync({
        knowledgebase_name: formData.name,
        folder_description: formData.description || undefined,
        chunking_config: JSON.stringify({
          type: formData.chunkingType,
          kwargs: {
            chunk_size: parseInt(formData.chunkSize),
            chunk_overlap: parseInt(formData.chunkOverlap),
          },
        }),
        embedding_config: JSON.stringify({
          type: formData.embeddingType,
          kwargs: {
            model: formData.embeddingModel,
          },
        }),
        vector_db_config: JSON.stringify(vectorDbConfig),
        files: uploadedFiles.map((f) => f.file),
      });

      onBack();
    } catch (error) {
      console.error("Error creating knowledge base:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <PageHeader 
          title={isEditMode ? "Edit Knowledge Base" : "Create New Knowledge Base"}
          onBack={onBack}
        />

        <div className="space-y-6">
          {/* Basic Information */}
          <BasicInformationCard
            name={formData.name}
            description={formData.description}
            onInputChange={handleInputChange}
          />

          {/* File Upload */}
          {!isEditMode && (
            <FileUploadCard
              uploadedFiles={uploadedFiles}
              onDrop={onDrop}
              onRemoveFile={removeFile}
            />
          )}

          {/* Vector Database Configuration */}
          <VectorDatabaseConfigurationCard
            formData={{
              useOwnWeaviate: formData.useOwnWeaviate,
              weaviateUrl: formData.weaviateUrl,
              weaviateApiKey: formData.weaviateApiKey,
              weaviateOpenAIApiKey: formData.weaviateOpenAIApiKey,
              weaviateGrpcHost: formData.weaviateGrpcHost,
              weaviateGrpcPort: formData.weaviateGrpcPort,
              weaviateHttpHost: formData.weaviateHttpHost,
              weaviateHttpPort: formData.weaviateHttpPort,
              weaviateGrpcSecure: formData.weaviateGrpcSecure,
              weaviateHttpSecure: formData.weaviateHttpSecure,
              weaviateIsEmbedded: formData.weaviateIsEmbedded,
              weaviateCollectionName: formData.weaviateCollectionName,
              weaviateTimeout: formData.weaviateTimeout,
              weaviateEmbeddingProvider: formData.weaviateEmbeddingProvider,
            }}
            onInputChange={handleInputChange}
            onSwitchChange={handleSwitchChange}
          />

          {/* Embedding Configuration */}
          <EmbeddingConfigurationCard
            embeddingType={formData.embeddingType}
            embeddingModel={formData.embeddingModel}
            onInputChange={handleInputChange}
          />

          {/* Chunking Configuration */}
          <ChunkingConfigurationCard
            chunkingType={formData.chunkingType}
            chunkSize={formData.chunkSize}
            chunkOverlap={formData.chunkOverlap}
            onInputChange={handleInputChange}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={onBack}
            disabled={createKnowledgeBase.isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!formData.name.trim() || createKnowledgeBase.isPending}
            className="bg-teal-800 hover:bg-teal-700 text-white"
          >
            {createKnowledgeBase.isPending
              ? "Creating..."
              : isEditMode
                ? "Update"
                : "Create"}
          </Button>
        </div>
      </div>
    </div>
  );
};
