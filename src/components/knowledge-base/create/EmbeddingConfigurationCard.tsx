import React from "react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface EmbeddingConfigurationCardProps {
  embeddingType: string;
  embeddingModel: string;
  onInputChange: (field: string, value: string) => void;
}

export const EmbeddingConfigurationCard: React.FC<
  EmbeddingConfigurationCardProps
> = ({ embeddingType, embeddingModel, onInputChange }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Embedding Configuration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="embeddingType">Embedding Type</Label>
          <Select
            value={embeddingType}
            onValueChange={(value) => onInputChange("embeddingType", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select embedding type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="OpenAIEmbeddings">
                OpenAI Embeddings
              </SelectItem>
              {/* <SelectItem value="HuggingFaceEmbeddings">HuggingFace Embeddings</SelectItem> */}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="embeddingModel">Embedding Model</Label>
          <Select
            value={embeddingModel}
            onValueChange={(value) => onInputChange("embeddingModel", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select embedding model" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="text-embedding-3-small">
                text-embedding-3-small
              </SelectItem>
              <SelectItem value="text-embedding-3-large">
                text-embedding-3-large
              </SelectItem>
              <SelectItem value="text-embedding-ada-002">
                text-embedding-ada-002
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
};
