import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";

interface VectorDatabaseFormData {
  useOwnWeaviate: boolean;
  weaviateUrl: string;
  weaviateApiKey: string;
  weaviateOpenAIApiKey: string;
  weaviateGrpcHost: string;
  weaviateGrpcPort: string;
  weaviateHttpHost: string;
  weaviateHttpPort: string;
  weaviateGrpcSecure: boolean;
  weaviateHttpSecure: boolean;
  weaviateIsEmbedded: boolean;
  weaviateCollectionName: string;
  weaviateTimeout: string;
  weaviateEmbeddingProvider: string;
}

interface VectorDatabaseConfigurationCardProps {
  formData: VectorDatabaseFormData;
  onInputChange: (field: string, value: string) => void;
  onSwitchChange: (field: string, value: boolean) => void;
}

export const VectorDatabaseConfigurationCard: React.FC<
  VectorDatabaseConfigurationCardProps
> = ({ formData, onInputChange, onSwitchChange }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Vector Database Configuration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <Label>Use my own Weaviate instance</Label>
            <p className="text-sm text-gray-600 mt-1">
              Bring your own vector database or use a fully managed option.
            </p>
          </div>
          <Switch
            checked={formData.useOwnWeaviate}
            onCheckedChange={(checked) =>
              onSwitchChange("useOwnWeaviate", checked)
            }
          />
        </div>

        {formData.useOwnWeaviate && (
          <div className="space-y-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="weaviateUrl">URL</Label>
                <Input
                  id="weaviateUrl"
                  type="url"
                  value={formData.weaviateUrl}
                  onChange={(e) => onInputChange("weaviateUrl", e.target.value)}
                  placeholder="https://your-weaviate-instance.com"
                />
              </div>
              <div>
                <Label htmlFor="weaviateApiKey">API Key</Label>
                <Input
                  id="weaviateApiKey"
                  type="password"
                  value={formData.weaviateApiKey}
                  onChange={(e) =>
                    onInputChange("weaviateApiKey", e.target.value)
                  }
                  placeholder="Your Weaviate API key"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="weaviateOpenAIApiKey">OpenAI API Key</Label>
              <Input
                id="weaviateOpenAIApiKey"
                type="password"
                value={formData.weaviateOpenAIApiKey}
                onChange={(e) =>
                  onInputChange("weaviateOpenAIApiKey", e.target.value)
                }
                placeholder="Your OpenAI API key"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="weaviateGrpcHost">GRPC Host</Label>
                <Input
                  id="weaviateGrpcHost"
                  value={formData.weaviateGrpcHost}
                  onChange={(e) =>
                    onInputChange("weaviateGrpcHost", e.target.value)
                  }
                  placeholder="grpc.weaviate.example.com"
                />
              </div>
              <div>
                <Label htmlFor="weaviateGrpcPort">GRPC Port</Label>
                <Input
                  id="weaviateGrpcPort"
                  type="number"
                  value={formData.weaviateGrpcPort}
                  onChange={(e) =>
                    onInputChange("weaviateGrpcPort", e.target.value)
                  }
                  placeholder="443"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="weaviateHttpHost">HTTP Host</Label>
                <Input
                  id="weaviateHttpHost"
                  value={formData.weaviateHttpHost}
                  onChange={(e) =>
                    onInputChange("weaviateHttpHost", e.target.value)
                  }
                  placeholder="weaviate.example.com"
                />
              </div>
              <div>
                <Label htmlFor="weaviateHttpPort">HTTP Port</Label>
                <Input
                  id="weaviateHttpPort"
                  type="number"
                  value={formData.weaviateHttpPort}
                  onChange={(e) =>
                    onInputChange("weaviateHttpPort", e.target.value)
                  }
                  placeholder="443"
                />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="weaviateGrpcSecure"
                  checked={formData.weaviateGrpcSecure}
                  onCheckedChange={(checked) =>
                    onSwitchChange("weaviateGrpcSecure", checked)
                  }
                />
                <Label htmlFor="weaviateGrpcSecure">GRPC Secure</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="weaviateHttpSecure"
                  checked={formData.weaviateHttpSecure}
                  onCheckedChange={(checked) =>
                    onSwitchChange("weaviateHttpSecure", checked)
                  }
                />
                <Label htmlFor="weaviateHttpSecure">HTTP Secure</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="weaviateIsEmbedded"
                  checked={formData.weaviateIsEmbedded}
                  onCheckedChange={(checked) =>
                    onSwitchChange("weaviateIsEmbedded", checked)
                  }
                />
                <Label htmlFor="weaviateIsEmbedded">Is Embedded</Label>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="weaviateCollectionName">Collection Name</Label>
                <Input
                  id="weaviateCollectionName"
                  value={formData.weaviateCollectionName}
                  onChange={(e) =>
                    onInputChange("weaviateCollectionName", e.target.value)
                  }
                  placeholder="Your_Collection_Name"
                />
              </div>
              <div>
                <Label htmlFor="weaviateTimeout">Timeout (seconds)</Label>
                <Input
                  id="weaviateTimeout"
                  type="number"
                  value={formData.weaviateTimeout}
                  onChange={(e) =>
                    onInputChange("weaviateTimeout", e.target.value)
                  }
                  placeholder="30"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="weaviateEmbeddingProvider">
                Embedding Provider
              </Label>
              <Select
                value={formData.weaviateEmbeddingProvider}
                onValueChange={(value) =>
                  onInputChange("weaviateEmbeddingProvider", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select embedding provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="cohere">Cohere</SelectItem>
                  <SelectItem value="huggingface">HuggingFace</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
