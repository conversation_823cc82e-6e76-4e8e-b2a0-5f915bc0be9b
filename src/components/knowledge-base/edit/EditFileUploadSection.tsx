import React from "react";
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileUploadCard } from "../shared/FileUploadCard";

interface UploadedFile {
  file: File;
  id: string;
}

interface EditFileUploadSectionProps {
  uploadedFiles: UploadedFile[];
  onDrop: (acceptedFiles: File[]) => void;
  onRemoveFile: (fileId: string) => void;
  onUploadFiles: () => void;
  isUploading: boolean;
}

export const EditFileUploadSection: React.FC<EditFileUploadSectionProps> = ({
  uploadedFiles,
  onDrop,
  onRemoveFile,
  onUploadFiles,
  isUploading,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Add New Files
        </CardTitle>
      </CardHeader>
      <CardContent>
        <FileUploadCard
          uploadedFiles={uploadedFiles}
          onDrop={onDrop}
          onRemoveFile={onRemoveFile}
        />
        {uploadedFiles.length > 0 && (
          <div className="flex justify-end pt-4 border-t mt-4">
            <Button
              onClick={onUploadFiles}
              disabled={isUploading}
              className="bg-teal-800 hover:bg-teal-700 text-white"
            >
              {isUploading ? "Uploading..." : `Upload ${uploadedFiles.length} File${uploadedFiles.length > 1 ? 's' : ''}`}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};