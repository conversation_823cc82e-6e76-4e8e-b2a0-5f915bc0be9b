import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  useUpdateKnowledgeBase,
  useKnowledgeBase,
  useKnowledgeBaseFiles,
  useDeleteFile,
  useBulkUploadFiles,
  useDownloadFile
} from "@/hooks/useKnowledgeBase";
import { KnowledgeBase, KnowledgeBaseFileMetadata } from "@/types/knowledge-base";
import { BasicInformationCard, PageHeader } from "../shared";
import { ExistingFilesList } from "./ExistingFilesList";
import { EditFileUploadSection } from "./EditFileUploadSection";
import { useToast } from "@/hooks/use-toast";

interface EditKnowledgeBasePageProps {
  knowledgeBaseId: string;
  onBack: () => void;
}

interface UploadedFile {
  file: File;
  id: string;
}

// Remove the local interface since we now have it in types

export const EditKnowledgeBasePage: React.FC<EditKnowledgeBasePageProps> = ({
  knowledgeBaseId,
  onBack,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
  });
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  const { data: knowledgeBase, isLoading: isLoadingKB } = useKnowledgeBase(knowledgeBaseId);
  const { data: filesData, isLoading: isLoadingFiles, refetch: refetchFiles } = useKnowledgeBaseFiles(knowledgeBaseId);
  const updateKnowledgeBase = useUpdateKnowledgeBase();
  const deleteFile = useDeleteFile();
  const bulkUploadFiles = useBulkUploadFiles();
  const downloadFile = useDownloadFile();
  const { toast } = useToast();

  // Initialize form when knowledge base data loads
  useEffect(() => {
    if (knowledgeBase) {
      setFormData({
        name: knowledgeBase.name,
        description: knowledgeBase.description || "",
      });
    }
  }, [knowledgeBase]);

  const onDrop = (acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map((file) => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
    }));
    setUploadedFiles((prev) => [...prev, ...newFiles]);
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleUpdateInfo = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Error",
        description: "Knowledge base name is required",
        variant: "destructive",
      });
      return;
    }

    try {
      await updateKnowledgeBase.mutateAsync({
        id: knowledgeBaseId,
        data: {
          name: formData.name,
          description: formData.description || undefined,
        },
      });
    } catch (error) {
      console.error("Error updating knowledge base:", error);
    }
  };

  const handleUploadFiles = async () => {
    if (uploadedFiles.length === 0) {
      toast({
        title: "Error",
        description: "Please select files to upload",
        variant: "destructive",
      });
      return;
    }

    try {
      await bulkUploadFiles.mutateAsync({
        folderId: knowledgeBaseId,
        files: uploadedFiles.map(f => f.file),
      });
      setUploadedFiles([]); // Clear uploaded files after successful upload
    } catch (error) {
      console.error("Error uploading files:", error);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    try {
      await deleteFile.mutateAsync(fileId);
      // The hook will automatically invalidate and refetch the files list
    } catch (error) {
      console.error("Error deleting file:", error);
    }
  };

  const handleDownloadFile = async (fileId: string) => {
    try {
      await downloadFile.mutateAsync(fileId);
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  };


  if (isLoadingKB) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-800 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading knowledge base...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!knowledgeBase) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-gray-600">Knowledge base not found</p>
              <Button onClick={onBack} className="mt-4">
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <PageHeader title="Edit Knowledge Base" onBack={onBack} />

        <div className="space-y-6">
          {/* Basic Information */}
          <BasicInformationCard
            name={formData.name}
            description={formData.description}
            onInputChange={handleInputChange}
            onSave={handleUpdateInfo}
            isSaving={updateKnowledgeBase.isPending}
          />

          <ExistingFilesList
            files={filesData?.files}
            isLoading={isLoadingFiles}
            onDeleteFile={handleDeleteFile}
            onDownloadFile={handleDownloadFile}
            isDeleting={deleteFile.isPending}
            isDownloading={downloadFile.isPending}
          />

          <EditFileUploadSection
            uploadedFiles={uploadedFiles}
            onDrop={onDrop}
            onRemoveFile={removeFile}
            onUploadFiles={handleUploadFiles}
            isUploading={bulkUploadFiles.isPending}
          />
        </div>
      </div>
    </div>
  );
};