import React from "react";
import { Button } from "@/components/ui/button";
import { Upload, Trash2, Download } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { KnowledgeBaseFileMetadata } from "@/types/knowledge-base";
import FileDisplay from "@/components/FileDisplay";

interface ExistingFilesListProps {
  files?: KnowledgeBaseFileMetadata[];
  isLoading: boolean;
  onDeleteFile: (fileId: string) => void;
  onDownloadFile: (fileId: string) => void;
  isDeleting: boolean;
  isDownloading: boolean;
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  if (bytes > k * k) {
    return `${Math.round(bytes / (k * k))}MB`;
  } else if (Math.round(bytes / k) < k) {
    return `${Math.round(bytes / k)} KB`;
  } else {
    return `${(bytes / k).toFixed(1)} KB`;
  }
};

export const ExistingFilesList: React.FC<ExistingFilesListProps> = ({
  files,
  isLoading,
  onDeleteFile,
  onDownloadFile,
  isDeleting,
  isDownloading,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Existing Files
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-800"></div>
          </div>
        ) : files && files.length > 0 ? (
          <div className="space-y-3">
            {files.map((file: KnowledgeBaseFileMetadata) => (
              <div
                key={file.file_id}
                className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <FileDisplay
                    fileName={file.filename}
                    mimeType={file.content_type}
                    size="md"
                    className="flex-1"
                    fileIconColor="text-teal-500"
                  />
                  <div className="flex flex-col">
                    <span className="text-xs text-gray-500 font-medium">
                      {formatFileSize(file.size)}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDownloadFile(file.file_id)}
                    disabled={isDownloading}
                    className="text-gray-400 hover:text-gray-600 hover:bg-gray-50"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDeleteFile(file.file_id)}
                    disabled={isDeleting}
                    className="text-gray-400 hover:text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No files found in this knowledge base
          </div>
        )}
      </CardContent>
    </Card>
  );
};