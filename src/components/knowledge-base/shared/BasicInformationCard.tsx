import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface BasicInformationCardProps {
  name: string;
  description: string;
  onInputChange: (field: string, value: string) => void;
  onSave?: () => void;
  isSaving?: boolean;
}

export const BasicInformationCard: React.FC<BasicInformationCardProps> = ({
  name,
  description,
  onInputChange,
  onSave,
  isSaving = false,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Basic Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="name">Index Name *</Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => onInputChange("name", e.target.value)}
            placeholder="Enter knowledge base name"
          />
        </div>
        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => onInputChange("description", e.target.value)}
            placeholder="Describe the purpose of this knowledge base"
            rows={3}
          />
        </div>
        {onSave && (
          <div className="flex justify-end pt-2">
            <Button
              onClick={onSave}
              disabled={!name.trim() || isSaving}
              className="bg-teal-800 hover:bg-teal-700 text-white"
            >
              {isSaving ? "Saving..." : "Save Information"}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
