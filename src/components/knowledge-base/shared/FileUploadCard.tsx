import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CloudUpload, Trash2 } from "lucide-react";
import { useDropzone } from "react-dropzone";
import FileDisplay from "@/components/FileDisplay";

interface UploadedFile {
  file: File;
  id: string;
}

interface FileUploadCardProps {
  uploadedFiles: UploadedFile[];
  onDrop: (acceptedFiles: File[]) => void;
  onRemoveFile: (fileId: string) => void;
}

export const FileUploadCard: React.FC<FileUploadCardProps> = ({
  uploadedFiles,
  onDrop,
  onRemoveFile,
}) => {
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Upload Documents</CardTitle>
      </CardHeader>
      <CardContent>
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
            isDragActive
              ? "border-teal-500 bg-teal-50"
              : "border-gray-300 hover:border-gray-400"
          }`}
        >
          <input {...getInputProps()} />
          <CloudUpload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          {isDragActive ? (
            <p>Drop the files here...</p>
          ) : (
            <div>
              <p className="text-lg font-medium text-gray-900 mb-2">
                Drop files here or click to browse
              </p>
              <p className="text-sm text-gray-600">
                Max 100MB files are allowed
              </p>
            </div>
          )}
        </div>

        {/* Uploaded Files List */}
        {uploadedFiles.length > 0 && (
          <div className="mt-4 space-y-2">
            <Label>Upload Data Source *</Label>
            <div className="max-h-32 overflow-y-auto space-y-3">
              {uploadedFiles.map((uploadedFile) => (
                <div
                  key={uploadedFile.id}
                  className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <FileDisplay
                      fileName={uploadedFile.file.name}
                      mimeType={uploadedFile.file.type}
                      size="md"
                      className="flex-1"
                      fileIconColor="text-teal-500"
                    />
                    <div className="flex flex-col">
                      <span className="text-xs text-gray-500 font-medium">
                        {uploadedFile.file.size > 1024 * 1024
                          ? `${Math.round(uploadedFile.file.size / (1024 * 1024))}MB`
                          : Math.round(uploadedFile.file.size / 1024) < 1024
                            ? `${Math.round(uploadedFile.file.size / 1024)} KB`
                            : `${(uploadedFile.file.size / 1024).toFixed(1)} KB`}
                      </span>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemoveFile(uploadedFile.id)}
                    className="text-gray-400 hover:text-gray-600 hover:bg-gray-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
