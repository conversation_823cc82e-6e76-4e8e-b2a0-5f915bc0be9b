import { useState, useEffect } from "react";
import { Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from "lucide-react";
import * as AlertDialog from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Model2 } from "@/types/model";

interface DeleteModelModalProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  model: Model2 | null;
  onDelete: () => void;
}

const DeleteModelModal = ({
  isOpen,
  setIsOpen,
  model,
  onDelete,
}: DeleteModelModalProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  // Reset deleting state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsDeleting(false);
    }
  }, [isOpen]);

  const handleDelete = async () => {
    if (!model) return;

    setIsDeleting(true);
    try {
      onDelete();
      toast({
        title: "Model deleted",
        description: `Model "${model.name}" has been successfully deleted.`,
      });
    } catch (error: unknown) {
      console.error("Failed to delete model:", error);

      // Handle specific error cases
      let errorMessage = "Failed to delete model. Please try again.";

      if (error instanceof Error) {
        const message = error.message;
        if (message.includes("deployed") || message.includes("active")) {
          errorMessage =
            "Cannot delete a deployed model. Please undeploy it first before deletion.";
        } else if (
          message.includes("permission") ||
          message.includes("unauthorized")
        ) {
          errorMessage = "You don't have permission to delete this model.";
        } else if (
          message.includes("in use") ||
          message.includes("referenced")
        ) {
          errorMessage =
            "This model is currently being used and cannot be deleted.";
        } else {
          errorMessage = message;
        }
      }

      toast({
        title: "Deletion failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (!model) return null;

  return (
    <AlertDialog.AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialog.AlertDialogContent className="max-w-md">
        <AlertDialog.AlertDialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <Trash2 className="h-5 w-5 text-red-600" />
            </div>
            <AlertDialog.AlertDialogTitle className="text-xl">
              Delete Model
            </AlertDialog.AlertDialogTitle>
          </div>
          <AlertDialog.AlertDialogDescription className="text-left space-y-4">
            <p className="text-slate-600">
              Are you sure you want to delete this model? This action cannot be
              undone.
            </p>
          </AlertDialog.AlertDialogDescription>
        </AlertDialog.AlertDialogHeader>

        <AlertDialog.AlertDialogFooter className="flex gap-2">
          <AlertDialog.AlertDialogCancel
            disabled={isDeleting}
            className="border-slate-300 text-slate-600 hover:bg-slate-50"
          >
            Cancel
          </AlertDialog.AlertDialogCancel>
          <AlertDialog.AlertDialogAction asChild>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  Delete Model
                </>
              )}
            </Button>
          </AlertDialog.AlertDialogAction>
        </AlertDialog.AlertDialogFooter>
      </AlertDialog.AlertDialogContent>
    </AlertDialog.AlertDialog>
  );
};

export default DeleteModelModal;
