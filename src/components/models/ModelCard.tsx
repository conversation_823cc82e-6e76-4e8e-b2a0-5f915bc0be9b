import { Trash2, Edit, GitFork } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Model2 } from "@/types/model";
import TooltipText from "../TooltipText";
import DeleteModelModal from "./DeleteModelModal";
import { useState } from "react";

interface ModelCardProps {
  model: Model2;
  onClick: (model: Model2) => void;
  onDelete: (modelId: string) => void;
  onFork?: (model: Model2) => void; // New prop for forking default models
  isDeleting?: boolean;
}

export const ModelCard: React.FC<ModelCardProps> = ({
  model,
  onClick,
  onDelete,
  onFork,
  isDeleting = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleDelete = () => onDelete(model.id);

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick(model);
  };

  const handleFork = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onFork) {
      onFork(model);
    }
  };

  return (
    <Card
      className={`hover:shadow-lg transition-all duration-300 bg-white border-slate-200 border-2 overflow-hidden group h-[240px]`}
    >
      <CardContent className="p-6 flex flex-col h-full justify-between">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex flex-col gap-2 max-w-[85%]">
              <h3 className="text-lg font-bold text-slate-900 transition-colors leading-tight truncate">
                {model.name}
              </h3>
              <div className="flex flex-row gap-2">
                {model.is_default && (
                  <Badge
                    variant="secondary"
                    className="w-fit bg-blue-50 text-blue-700 border-blue-200 text-xs"
                  >
                    Default
                  </Badge>
                )}
                <Badge
                  variant="secondary"
                  className="w-fit bg-green-50 text-green-700 border-green-200 text-xs"
                >
                  {model.modelId}
                </Badge>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <p className="text-slate-600 text-sm leading-relaxed line-clamp-3">
              {model.description || "No description available"}
            </p>
            <div className="flex gap-2 items-center text-xs text-slate-500"></div>
          </div>
        </div>

        <div className="flex items-center justify-between pt-4 border-t border-slate-100">
          <div className="text-xs text-slate-400">
            Modified {model.lastModified}
          </div>

          <div>
            {model.is_default ? (
              // For default models: show Fork & Edit option
              <TooltipText text="Fork & Edit">
                <Button size="icon" variant="ghost" onClick={handleFork}>
                  <GitFork className="h-4 w-4" />
                </Button>
              </TooltipText>
            ) : (
              // For non-default models: show Edit and Delete options
              <>
                <TooltipText text="Edit">
                  <Button size="icon" variant="ghost" onClick={handleEdit}>
                    <Edit className="h-4 w-4" />
                  </Button>
                </TooltipText>
                <TooltipText text="Delete">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="text-red-600"
                    onClick={() => setIsOpen(true)}
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TooltipText>
                <DeleteModelModal
                  model={model}
                  isOpen={isOpen}
                  setIsOpen={setIsOpen}
                  onDelete={handleDelete}
                />
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
