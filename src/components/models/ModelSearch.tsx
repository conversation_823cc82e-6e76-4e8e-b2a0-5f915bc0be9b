import { Search, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";

interface ModelSearchProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  isSearching: boolean;
  placeholder?: string;
}

export const ModelSearch: React.FC<ModelSearchProps> = ({
  searchTerm,
  onSearchChange,
  isSearching,
  placeholder = "Search models...",
}) => {
  return (
    <div className="relative flex-1">
      <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
      <Input
        placeholder={placeholder}
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        className="pl-12 pr-12 h-14 border-slate-300 focus:border-teal-500 focus:ring-teal-500 text-base"
      />
      {isSearching && (
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
          <Loader2 className="h-5 w-5 text-slate-400 animate-spin" />
        </div>
      )}
      {isSearching && (
        <div className="absolute left-0 right-0 top-full mt-2">
          <Skeleton className="h-2 w-full" />
        </div>
      )}
    </div>
  );
};
