import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Key, Info, Eye, EyeOff } from "lucide-react";

interface ApiRequirement {
  provider_type: string;
  provider_name: string;
  context_type: string;
  context_name: string;
  required_fields: string[];
  optional_fields: string[];
  missing_fields: string[];
  present_fields: string[];
  model: string;
}

interface ApiKeyCollectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (apiKeys: Record<string, Record<string, string>>) => void;
  templateName: string;
  apiRequirements: {
    required_providers: string[];
    agent_requirements: ApiRequirement[];
    team_requirements: ApiRequirement | null;
  };
  isLoading?: boolean;
}

export const ApiKeyCollectionModal = ({
  isOpen,
  onClose,
  onConfirm,
  templateName,
  apiRequirements,
  isLoading = false,
}: ApiKeyCollectionModalProps) => {
  const [apiKeys, setApiKeys] = useState<Record<string, Record<string, string>>>({});
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get all requirements (team + agents) with unique keys
  const allRequirements: Array<ApiRequirement & { uniqueKey: string }> = [];

  if (apiRequirements.team_requirements) {
    allRequirements.push({
      ...apiRequirements.team_requirements,
      uniqueKey: `team_${apiRequirements.team_requirements.provider_type}`
    });
  }

  apiRequirements.agent_requirements.forEach((req, index) => {
    allRequirements.push({
      ...req,
      uniqueKey: `agent_${index}_${req.provider_type}`
    });
  });

  const handleFieldChange = (uniqueKey: string, field: string, value: string) => {
    setApiKeys(prev => ({
      ...prev,
      [uniqueKey]: {
        ...prev[uniqueKey],
        [field]: value,
      },
    }));

    // Clear error when user starts typing
    if (errors[`${uniqueKey}.${field}`]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${uniqueKey}.${field}`];
        return newErrors;
      });
    }
  };

  const togglePasswordVisibility = (uniqueKey: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [uniqueKey]: !prev[uniqueKey],
    }));
  };

  const validateInputs = (): boolean => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    allRequirements.forEach(req => {
      req.required_fields.forEach(field => {
        const value = apiKeys[req.uniqueKey]?.[field];
        if (!value || value.trim() === '') {
          newErrors[`${req.uniqueKey}.${field}`] = `${field.replace('_', ' ')} is required`;
          isValid = false;
        }
      });
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleConfirm = () => {
    if (validateInputs()) {
      onConfirm(apiKeys);
    }
  };

  const getFieldLabel = (field: string): string => {
    const labels: Record<string, string> = {
      api_key: "API Key",
      azure_endpoint: "Azure Endpoint",
      azure_deployment: "Azure Deployment",
      api_version: "API Version",
      base_url: "Base URL",
      organization: "Organization ID",
    };
    return labels[field] || field.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getFieldPlaceholder = (field: string): string => {
    const placeholders: Record<string, string> = {
      api_key: "api-key",
      azure_endpoint: "https://your-resource.openai.azure.com/",
      azure_deployment: "gpt-4o-mini",
      api_version: "2024-02-01",
      base_url: "https://api.openai.com/v1",
      organization: "org-...",
    };
    return placeholders[field] || "";
  };

  const isPasswordField = (field: string): boolean => {
    return field.includes('key') || field.includes('secret');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Configure API Keys for "{templateName}"
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              This template requires API keys to function. Please provide your API credentials below.
              These will be securely stored with your team configuration.
            </AlertDescription>
          </Alert>

          {allRequirements.map((requirement) => (
            <Card key={requirement.uniqueKey}>
              <CardHeader>
                <CardTitle className="text-lg">
                  {requirement.provider_name}
                  <span className="text-sm font-normal text-muted-foreground ml-2">
                    ({requirement.model})
                  </span>
                  {requirement.context_type === 'agent' && (
                    <span className="text-sm font-normal text-blue-600 ml-2">
                      - {requirement.context_name}
                    </span>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Required fields */}
                {requirement.required_fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={`${requirement.uniqueKey}-${field}`}>
                      {getFieldLabel(field)} <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id={`${requirement.uniqueKey}-${field}`}
                        type={isPasswordField(field) && !showPasswords[requirement.uniqueKey] ? "password" : "text"}
                        placeholder={getFieldPlaceholder(field)}
                        value={apiKeys[requirement.uniqueKey]?.[field] || ""}
                        onChange={(e) => handleFieldChange(requirement.uniqueKey, field, e.target.value)}
                        className={errors[`${requirement.uniqueKey}.${field}`] ? "border-red-500" : ""}
                      />
                      {isPasswordField(field) && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => togglePasswordVisibility(requirement.uniqueKey)}
                        >
                          {showPasswords[requirement.uniqueKey] ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      )}
                    </div>
                    {errors[`${requirement.uniqueKey}.${field}`] && (
                      <p className="text-sm text-red-500">{errors[`${requirement.uniqueKey}.${field}`]}</p>
                    )}
                  </div>
                ))}

                {/* Optional fields */}
                {requirement.optional_fields.map(field => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={`${requirement.uniqueKey}-${field}`}>
                      {getFieldLabel(field)} <span className="text-muted-foreground">(optional)</span>
                    </Label>
                    <Input
                      id={`${requirement.uniqueKey}-${field}`}
                      type="text"
                      placeholder={getFieldPlaceholder(field)}
                      value={apiKeys[requirement.uniqueKey]?.[field] || ""}
                      onChange={(e) => handleFieldChange(requirement.uniqueKey, field, e.target.value)}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          ))}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Creating Team...
              </>
            ) : (
              "Create Team"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};