import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, Search, Loader2 } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useTemplates, useInstantiateTemplate } from "@/hooks/useTemplates";
import { useToast } from "@/hooks/use-toast";
import { TemplateResponse, TemplateCategory } from "@/types/template";
import { ApiKeyCollectionModal } from "./ApiKeyCollectionModal";
import { TemplateCard } from "./TemplateCard";
import { templatesApi } from "@/api/templates";

interface BrowseTemplatesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Convert backend TemplateResponse to UI display format
const formatTemplateForDisplay = (template: TemplateResponse) => ({
  id: template.id,
  title: template.name,
  description: template.description || "No description available",
  category: template.category || "Custom",
  tags: template.tags || [],
});

// Scratch template for "Start from Scratch" option
const scratchTemplate = {
  id: "scratch",
  title: "Start from Scratch",
  description: "Start with a blank template and build your workflow from the ground up",
  category: "Custom",
  tags: ["custom", "blank"],
};

export const BrowseTemplatesModal = ({ isOpen, onClose }: BrowseTemplatesModalProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [instantiatingId, setInstantiatingId] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<typeof scratchTemplate | null>(null);
  const [apiRequirements, setApiRequirements] = useState<{
    required_providers: string[];
    agent_requirements: Array<{
      provider_type: string;
      provider_name: string;
      context_type: string;
      context_name: string;
      required_fields: string[];
      optional_fields: string[];
      missing_fields: string[];
      present_fields: string[];
      model: string;
    }>;
    team_requirements: {
      provider_type: string;
      provider_name: string;
      context_type: string;
      context_name: string;
      required_fields: string[];
      optional_fields: string[];
      missing_fields: string[];
      present_fields: string[];
      model: string;
    } | null;
  } | null>(null);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [loadingApiRequirements, setLoadingApiRequirements] = useState(false);

  // Hooks for data fetching
  const {
    data: templatesData,
    isLoading: templatesLoading,
    error: templatesError,
  } = useTemplates({
    search: searchTerm || undefined,
    limit: 50, // Load more templates for browsing
  });

  // Mutations for template actions
  const instantiateTemplate = useInstantiateTemplate();

  // Process templates data
  const allTemplates = [scratchTemplate, ...(templatesData?.items?.map(formatTemplateForDisplay) || [])];

  // Filter templates based on search
  const filteredTemplates = allTemplates.filter((template) => {
    const matchesSearch =
      searchTerm === "" ||
      template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesSearch;
  });

  // Reset filters when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm("");
      setInstantiatingId(null);
    }
  }, [isOpen]);

  const handleTemplateClick = async (template: typeof scratchTemplate) => {
    if (instantiatingId || loadingApiRequirements) {
      return;
    }

    if (template.id === "scratch") {
      onClose();
      navigate("/create");
      return;
    }

    setSelectedTemplate(template);
    setLoadingApiRequirements(true);

    try {
      // Check if template requires API keys
      const apiReqs = await templatesApi.getApiRequirements(template.id);

      if (apiReqs.required_providers.length > 0) {
        // Template requires API keys - show collection modal
        setApiRequirements(apiReqs);
        setShowApiKeyModal(true);
      } else {
        // No API keys required - proceed directly
        await createTeamFromTemplate(template, {});
      }
    } catch (error: unknown) {
      console.error("Failed to analyze template:", error);
      toast({
        title: "Template Analysis Failed",
        description: error instanceof Error ? error.message : "Failed to analyze template requirements. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoadingApiRequirements(false);
    }
  };

  const createTeamFromTemplate = async (
    template: typeof scratchTemplate,
    apiKeys: Record<string, Record<string, string>>
  ) => {
    if (instantiatingId) return;

    setInstantiatingId(template.id);

    try {
      // Generate customizations from API keys if provided
      let customizations = {};
      if (Object.keys(apiKeys).length > 0 && apiRequirements) {
        customizations = templatesApi.generateApiKeyCustomizations(apiRequirements, apiKeys);
      }

      // Instantiate the template
      const result = await instantiateTemplate.mutateAsync({
        template_id: template.id,
        name: `${template.title} Workflow`,
        customizations,
      });

      toast({
        title: "Template Instantiated",
        description: `"${template.title}" has been created as a new workflow.`,
      });

      // Close both modals and navigate
      setShowApiKeyModal(false);
      onClose();
      navigate(`/team/edit/${result.team_id}`);
    } catch (error: unknown) {
      console.error("Failed to instantiate template:", error);
      toast({
        title: "Instantiation Failed",
        description: error instanceof Error ? error.message : "Failed to create workflow from template. Please try again.",
        variant: "destructive",
      });
    } finally {
      setInstantiatingId(null);
    }
  };

  const handleApiKeyConfirm = async (apiKeys: Record<string, Record<string, string>>) => {
    if (selectedTemplate) {
      await createTeamFromTemplate(selectedTemplate, apiKeys);
    }
  };

  const handleApiKeyModalClose = () => {
    setShowApiKeyModal(false);
    setSelectedTemplate(null);
    setApiRequirements(null);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-2xl font-semibold">Browse Templates</DialogTitle>
        </DialogHeader>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4 pb-4 border-b">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
            <Input
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {templatesLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
              <span className="ml-2 text-slate-600">Loading templates...</span>
            </div>
          ) : templatesError ? (
            <div className="text-center py-12">
              <p className="text-red-500 text-lg mb-2">Failed to load templates</p>
              <p className="text-slate-400 text-sm">Please try refreshing the page</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-1">
              {filteredTemplates.map((template) => (
                <div key={template.id} className="relative">
                  <TemplateCard
                    icon={
                      template.id === "scratch" ? (
                        <Plus className="h-5 w-5 text-blue-600" />
                      ) : (
                        <span className="text-blue-600 font-semibold text-sm">T</span>
                      )
                    }
                    title={template.title}
                    description={template.description}
                    tag={template.category}
                    onClick={() => handleTemplateClick(template)}
                    variant={template.id === "scratch" ? "default" : "gray"}
                    className={instantiatingId === template.id || (loadingApiRequirements && selectedTemplate?.id === template.id) ? "opacity-50" : ""}
                  />
                  {(instantiatingId === template.id || (loadingApiRequirements && selectedTemplate?.id === template.id)) && (
                    <div className="absolute inset-0 bg-white/80 rounded-lg flex items-center justify-center">
                      <div className="flex items-center space-x-2">
                        <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                        <span className="text-blue-600 font-medium">
                          {loadingApiRequirements && selectedTemplate?.id === template.id
                            ? "Analyzing..."
                            : "Creating..."
                          }
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {!templatesLoading && !templatesError && filteredTemplates.length === 0 && (
            <div className="text-center py-12">
              <p className="text-slate-500 text-lg">No templates found matching your search.</p>
              <p className="text-slate-400 text-sm mt-2">Try adjusting your search terms.</p>
            </div>
          )}
        </div>
      </DialogContent>

      {/* API Key Collection Modal */}
      {showApiKeyModal && selectedTemplate && apiRequirements && (
        <ApiKeyCollectionModal
          isOpen={showApiKeyModal}
          onClose={handleApiKeyModalClose}
          onConfirm={handleApiKeyConfirm}
          templateName={selectedTemplate.title}
          apiRequirements={apiRequirements}
          isLoading={instantiatingId === selectedTemplate.id}
        />
      )}
    </Dialog>
  );
};
