import { ReactNode } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface TemplateCardProps {
  icon: ReactNode;
  title: string;
  description: string;
  buttonText?: string;
  buttonVariant?: "default" | "outline";
  tag?: string;
  onClick?: () => void;
  href?: string;
  className?: string;
  variant?: "default" | "gray";
}

export const TemplateCard = ({
  icon,
  title,
  description,
  buttonText,
  buttonVariant = "default",
  tag,
  onClick,
  className = "",
  variant = "default",
}: TemplateCardProps) => {
  const bgColor = variant === "gray" ? "bg-gray-50" : "bg-white";

  return (
    <Card
      className={`hover:shadow-lg transition-all duration-300 ${bgColor} border-slate-200 border cursor-pointer group h-full ${className}`}
      onClick={onClick}
    >
      <CardContent className="p-6 h-full flex flex-col">
        {/* Icon */}
        <div className="w-10 h-10 bg-slate-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-slate-200 transition-colors border border-slate-200">
          {icon}
        </div>

        {/* Content */}
        <div className="flex-grow flex flex-col">
          <h3 className="text-lg font-semibold text-slate-900 mb-2 text-left">{title}</h3>
          <p className="text-slate-600 text-sm mb-4 flex-grow leading-relaxed text-left">{description}</p>
        </div>

        {/* Button */}
        {buttonText && (
          <Button
            className={
              buttonVariant === "default"
                ? "bg-teal-800 hover:bg-teal-700 text-white font-medium w-full"
                : "border-teal-800 text-teal-700 hover:bg-green-50 hover:border-green-700 hover:text-green-700 font-medium w-full"
            }
            variant={buttonVariant}
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onClick?.();
            }}
          >
            {buttonText}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};
