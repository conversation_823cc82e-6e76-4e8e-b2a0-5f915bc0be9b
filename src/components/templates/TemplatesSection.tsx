import { useState } from "react";
import { Plus, FileText, MoveRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { TemplateCard } from "./TemplateCard";
import { BrowseTemplatesModal } from "./BrowseTemplatesModal";
import { ApiKeyCollectionModal } from "./ApiKeyCollectionModal";
import { Link, useNavigate } from "react-router-dom";
import { useTemplates, useInstantiateTemplate } from "@/hooks/useTemplates";
import { useToast } from "@/hooks/use-toast";
import { templatesApi } from "@/api/templates";

export const TemplatesSection = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [instantiatingId, setInstantiatingId] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<{id: string; name: string} | null>(null);
  const [apiRequirements, setApiRequirements] = useState<{
    required_providers: string[];
    agent_requirements: Array<{
      provider_type: string;
      provider_name: string;
      context_type: string;
      context_name: string;
      required_fields: string[];
      optional_fields: string[];
      missing_fields: string[];
      present_fields: string[];
      model: string;
    }>;
    team_requirements: {
      provider_type: string;
      provider_name: string;
      context_type: string;
      context_name: string;
      required_fields: string[];
      optional_fields: string[];
      missing_fields: string[];
      present_fields: string[];
      model: string;
    } | null;
  } | null>(null);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [loadingApiRequirements, setLoadingApiRequirements] = useState(false);

  // Fetch recent templates (limited number for display)
  const {
    data: templatesData,
    isLoading,
    error,
  } = useTemplates({
    limit: 2, // Only show first 2 templates
    sort_by: ["created_at"],
    sort_order: "desc",
  });

  // Mutations for template actions
  const instantiateTemplate = useInstantiateTemplate();

  // Get first 2 templates to display alongside "Start from Scratch"
  const recentTemplates = templatesData?.items?.slice(0, 2) || [];

  // Handle template instantiation
  const handleTemplateClick = async (templateId: string, templateName: string) => {
    if (instantiatingId || loadingApiRequirements) {
      return;
    }

    setSelectedTemplate({ id: templateId, name: templateName });
    setLoadingApiRequirements(true);

    try {
      // Check if template requires API keys
      const apiReqs = await templatesApi.getApiRequirements(templateId);

      if (apiReqs.required_providers.length > 0) {
        // Template requires API keys - show collection modal
        setApiRequirements(apiReqs);
        setShowApiKeyModal(true);
      } else {
        // No API keys required - proceed directly
        await createTeamFromTemplate(templateId, templateName, {});
      }
    } catch (error: unknown) {
      console.error("Failed to analyze template:", error);
      toast({
        title: "Template Analysis Failed",
        description: error instanceof Error ? error.message : "Failed to analyze template requirements. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoadingApiRequirements(false);
    }
  };

  const createTeamFromTemplate = async (
    templateId: string,
    templateName: string,
    apiKeys: Record<string, Record<string, string>>
  ) => {
    if (instantiatingId) return;

    setInstantiatingId(templateId);

    try {
      // Generate customizations from API keys if provided
      let customizations = {};
      if (Object.keys(apiKeys).length > 0 && apiRequirements) {
        customizations = templatesApi.generateApiKeyCustomizations(apiRequirements, apiKeys);
      }

      // Instantiate the template
      const result = await instantiateTemplate.mutateAsync({
        template_id: templateId,
        name: `${templateName} Workflow`,
        customizations,
      });

      toast({
        title: "Template Instantiated",
        description: `"${templateName}" has been created as a new workflow.`,
      });

      // Close modal and navigate
      setShowApiKeyModal(false);
      navigate(`/team/edit/${result.team_id}`);
    } catch (error: unknown) {
      console.error("Failed to instantiate template:", error);
      toast({
        title: "Instantiation Failed",
        description: error instanceof Error ? error.message : "Failed to create workflow from template. Please try again.",
        variant: "destructive",
      });
    } finally {
      setInstantiatingId(null);
    }
  };

  const handleApiKeyConfirm = async (apiKeys: Record<string, Record<string, string>>) => {
    if (selectedTemplate) {
      await createTeamFromTemplate(selectedTemplate.id, selectedTemplate.name, apiKeys);
    }
  };

  const handleApiKeyModalClose = () => {
    setShowApiKeyModal(false);
    setSelectedTemplate(null);
    setApiRequirements(null);
  };

  return (
    <div className="p-6">
      {/* Main Content Card */}
      <div className="bg-white rounded-lg p-8 shadow-sm">
        {/* Template Section Header */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-semibold text-slate-900 mb-2">Start creating from the templates</h2>
          <p className="text-slate-600">
            Click and deploy AI assistants, chatbots, workflow automations, and more instantly
          </p>
        </div>

        {/* Template Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Start from Scratch Template */}
          <Link to="/create">
            <TemplateCard
              icon={<Plus className="h-6 w-6 text-slate-700" />}
              title="Start from Scratch"
              description="Start with a blank template"
              buttonText="Get Started"
              buttonVariant="default"
            />
          </Link>

          {/* Recent Templates */}
          {isLoading
            ? // Loading placeholders
              Array.from({ length: 2 }).map((_, index) => (
                <div key={index} className="border border-slate-200 rounded-lg p-6 animate-pulse">
                  <div className="w-12 h-12 bg-slate-200 rounded-lg mb-4"></div>
                  <div className="h-4 bg-slate-200 rounded mb-2"></div>
                  <div className="h-3 bg-slate-200 rounded mb-4"></div>
                </div>
              ))
            : recentTemplates.length > 0
            ? recentTemplates.map((template) => (
                <TemplateCard
                  key={template.id}
                  icon={<FileText className="h-6 w-6 text-slate-700" />}
                  title={template.name}
                  description={template.description || "No description available"}
                  tag={template.category}
                  onClick={() => handleTemplateClick(template.id, template.name)}
                  variant="gray"
                  className={
                    instantiatingId === template.id ||
                    (loadingApiRequirements && selectedTemplate?.id === template.id)
                      ? "opacity-70 pointer-events-none"
                      : ""
                  }
                />
              ))
            : // Empty state placeholders when no templates exist
              Array.from({ length: 2 }).map((_, index) => (
                <div key={index} className="border border-dashed border-slate-300 rounded-lg p-6 text-center">
                  <FileText className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-500 text-sm">No templates available</p>
                  <p className="text-slate-400 text-xs">Create your first template to see it here</p>
                </div>
              ))}

          {/* If less than 2 templates, show placeholder */}
          {!isLoading && recentTemplates.length === 1 && (
            <div className="border border-dashed border-slate-300 rounded-lg p-6 text-center">
              <FileText className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-500 text-sm">Create more templates</p>
              <p className="text-slate-400 text-xs">Your templates will appear here</p>
            </div>
          )}
        </div>

        {/* Browse Templates Button */}
        <div className="text-center">
          <Button
            onClick={() => setIsModalOpen(true)}
            variant="ghost"
            size="lg"
            className="text-slate-700"
          >
            Browse All Templates
            <MoveRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Browse Templates Modal */}
      <BrowseTemplatesModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />

      {/* API Key Collection Modal */}
      {showApiKeyModal && selectedTemplate && apiRequirements && (
        <ApiKeyCollectionModal
          isOpen={showApiKeyModal}
          onClose={handleApiKeyModalClose}
          onConfirm={handleApiKeyConfirm}
          templateName={selectedTemplate.name}
          apiRequirements={apiRequirements}
          isLoading={instantiatingId === selectedTemplate.id}
        />
      )}
    </div>
  );
};
