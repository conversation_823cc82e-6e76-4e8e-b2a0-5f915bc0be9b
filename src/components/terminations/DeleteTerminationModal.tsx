import { useState, useEffect } from "react";
import { Trash2, <PERSON>ert<PERSON><PERSON>gle, Loader2 } from "lucide-react";
import * as AlertDialog from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Termination2 } from "@/types/termination";

interface DeleteTerminationModalProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  termination: Termination2 | null;
  onDelete: () => void;
}

const DeleteTerminationModal = ({
  isOpen,
  setIsOpen,
  termination,
  onDelete,
}: DeleteTerminationModalProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  // Reset deleting state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsDeleting(false);
    }
  }, [isOpen]);

  const handleDelete = async () => {
    if (!termination) return;

    setIsDeleting(true);
    try {
      await onDelete();
      toast({
        title: "Termination condition deleted",
        description: `${termination.name} has been successfully deleted.`,
      });
      setIsOpen(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error?.message || "Failed to delete termination condition",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (!termination) return null;

  return (
    <AlertDialog.AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialog.AlertDialogContent className="max-w-md">
        <AlertDialog.AlertDialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <Trash2 className="h-5 w-5 text-red-600" />
            </div>
            <AlertDialog.AlertDialogTitle className="text-xl">
              Delete Termination Condition
            </AlertDialog.AlertDialogTitle>
          </div>
          <AlertDialog.AlertDialogDescription className="text-left space-y-4">
            <p className="text-slate-600">
              Are you sure you want to delete this termination condition? This
              action cannot be undone.
            </p>
          </AlertDialog.AlertDialogDescription>
        </AlertDialog.AlertDialogHeader>

        <AlertDialog.AlertDialogFooter className="flex gap-2">
          <AlertDialog.AlertDialogCancel
            disabled={isDeleting}
            className="border-slate-300 text-slate-600 hover:bg-slate-50"
          >
            Cancel
          </AlertDialog.AlertDialogCancel>
          <AlertDialog.AlertDialogAction asChild>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  Delete Termination Condition
                </>
              )}
            </Button>
          </AlertDialog.AlertDialogAction>
        </AlertDialog.AlertDialogFooter>
      </AlertDialog.AlertDialogContent>
    </AlertDialog.AlertDialog>
  );
};

export default DeleteTerminationModal;
