import { Trash2, Edit, GitFork } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tool2 } from "@/types/tool";
import TooltipText from "../TooltipText";
import DeleteToolModal from "./DeleteToolModal";
import { useState } from "react";

interface ToolCardProps {
  tool: Tool2;
  onClick: (tool: Tool2) => void;
  onDelete: (toolId: string) => void;
  onFork?: (tool: Tool2) => void;
  isDeleting?: boolean;
}

export const ToolCard: React.FC<ToolCardProps> = ({
  tool,
  onClick,
  onDelete,
  onFork,
  isDeleting = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleDelete = () => onDelete(tool.id);

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick(tool);
  };

  const handleFork = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onFork) {
      onFork(tool);
    }
  };

  return (
    <Card
      className={`hover:shadow-lg transition-all duration-300 bg-white border-slate-200 border-2 overflow-hidden group h-[240px]`}
    >
      <CardContent className="p-6 flex flex-col h-full justify-between">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex flex-col gap-2 max-w-[85%]">
              <h3 className="text-lg font-bold text-slate-900 transition-colors leading-tight truncate">
                {tool.name}
              </h3>
              <div className="flex flex-row gap-2">
                {tool.is_default && (
                  <Badge
                    variant="secondary"
                    className="w-fit bg-blue-50 text-blue-700 border-blue-200 text-xs"
                  >
                    Default
                  </Badge>
                )}
                <Badge
                  variant="secondary"
                  className="w-fit bg-green-50 text-green-700 border-green-200 text-xs"
                >
                  {tool.status}
                </Badge>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <p className="text-slate-600 text-sm leading-relaxed line-clamp-3">
              {tool.description || "No description available"}
            </p>
          </div>
        </div>

        <div className="flex items-center justify-between pt-4 border-t border-slate-100">
          <div className="text-xs text-slate-400">
            Modified {tool.lastModified}
          </div>

          <div>
            {tool.is_default ? (
              <TooltipText text="Fork & Edit">
                <Button size="icon" variant="ghost" onClick={handleFork}>
                  <GitFork className="h-4 w-4" />
                </Button>
              </TooltipText>
            ) : (
              <>
                <TooltipText text="Edit">
                  <Button size="icon" variant="ghost" onClick={handleEdit}>
                    <Edit className="h-4 w-4" />
                  </Button>
                </TooltipText>
                <TooltipText text="Delete">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="text-red-600"
                    onClick={() => setIsOpen(true)}
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TooltipText>
                <DeleteToolModal
                  tool={tool}
                  isOpen={isOpen}
                  setIsOpen={setIsOpen}
                  onDelete={handleDelete}
                />
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
