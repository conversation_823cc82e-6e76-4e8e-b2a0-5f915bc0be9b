import { Search, Loader2, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ToolSearchProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  isSearching: boolean;
  placeholder?: string;
  statusFilter: string;
  setStatusFilter: (val: string) => void;
}

export const ToolSearch: React.FC<ToolSearchProps> = ({
  searchTerm,
  onSearchChange,
  isSearching,
  statusFilter,
  setStatusFilter,
  placeholder = "Search tools...",
}) => {
  return (
    <div className="flex gap-4 mb-8">
      <div className="relative flex-1">
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
        <Input
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-12 pr-12 h-14 border-slate-300 focus:border-teal-500 focus:ring-teal-500 text-base"
        />
        {isSearching && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            <Loader2 className="h-5 w-5 text-slate-400 animate-spin" />
          </div>
        )}
      </div>
      <Select value={statusFilter} onValueChange={setStatusFilter}>
        <SelectTrigger className="w-fit h-14 border-slate-300 text-base flex gap-2">
          <Filter className="h-5 w-5" />
          <SelectValue placeholder="Filter by status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Tools</SelectItem>
          <SelectItem value="active">Active</SelectItem>
          <SelectItem value="inactive">Inactive</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};
