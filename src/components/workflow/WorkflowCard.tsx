import { type Team2 } from "@/types/team";
import { Card, CardContent } from "../ui/card";
import { Separator } from "../ui/separator";
import { useCurrentUser } from "@/hooks/useUser";
import TooltipText from "../TooltipText";
import { But<PERSON> } from "../ui/button";
import { Copy, Edit, ExternalLink, Loader2, Play, Trash2 } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import {
  useCreateTeam,
  useDeployTeam,
  useUndeployTeam,
} from "@/hooks/useTeams";
import { useToast } from "@/hooks/use-toast";
import { useWorkflow } from "@/contexts/WorkflowContext";
import { useState } from "react";

type Props = {
  team: Team2;
  handleDeleteTeam: (team: Team2) => void;
};

export default function WorkflowCard({ team, handleDeleteTeam }: Props) {
  const navigate = useNavigate();
  const { data: user } = useCurrentUser();
  const { toast } = useToast();
  // Track which team is being duplicated for loading state
  const [duplicatingTeamId, setDuplicatingTeamId] = useState<string | null>(
    null
  );

  const { addNewTeam } = useWorkflow();

  // Initialize the create team mutation for duplicate functionality
  const createTeamMutation = useCreateTeam(addNewTeam);
  const undeployTeamMutation = useUndeployTeam();
  const deployTeamMutation = useDeployTeam();

  const handleEditTeam = (team: Team2) => {
    navigate(`/team/edit/${team.id}`, { state: { teamData: team } });
  };

  // Duplicate team function
  const handleDuplicateTeam = async (teamId: string, teamName: string) => {
    // Prevent multiple duplications at once
    if (duplicatingTeamId) {
      return;
    }

    // Set loading state for this specific team
    setDuplicatingTeamId(teamId);

    try {
      // Fetch the complete team data using the API directly
      const { teamsApi } = await import("@/api/teams");
      const teamResponse = await teamsApi.getById(teamId);

      if (!teamResponse) {
        toast({
          title: "Error",
          description: "Team data not available for duplication.",
          variant: "destructive",
        });
        return;
      }

      // Create a payload similar to CreateTeam.tsx saveChanges function
      const originalComponent = teamResponse.component;

      // Create the duplicate payload with the same structure as CreateTeam
      const payload: any = {
        component: {
          component_type: "team" as const,
          component_version: 1,
          config: {
            // Copy all config from original team
            ...originalComponent?.config,
          },
          description: `${
            originalComponent?.description || "A team workflow"
          } (Copy)`,
          label: `${originalComponent?.label || "Workflow Team"} (Copy)`,
          provider:
            originalComponent?.provider ||
            "autogen_agentchat.teams.RoundRobinGroupChat",
          version: 1,
        },
        organization_id: "123e4567-e89b-12d3-a456-426614174000", // TODO: Get from user context
        model_id: (teamResponse as any).model_id,
      };

      // Include agent IDs if they exist
      const teamAgentIds = (teamResponse as any).team_agent_ids;
      if (
        Array.isArray(teamAgentIds) &&
        teamAgentIds.filter(Boolean).length > 0
      ) {
        payload.team_agent_ids = teamAgentIds.filter(Boolean);
      }

      // Include input IDs if they exist
      const teamInputIds = (teamResponse as any).team_input_ids;
      if (
        Array.isArray(teamInputIds) &&
        teamInputIds.filter(Boolean).length > 0
      ) {
        payload.team_input_ids = teamInputIds.filter(Boolean);
      }

      // Include output IDs if they exist
      const teamOutputIds = (teamResponse as any).team_output_ids;
      if (
        Array.isArray(teamOutputIds) &&
        teamOutputIds.filter(Boolean).length > 0
      ) {
        payload.team_output_ids = teamOutputIds.filter(Boolean);
      }

      // Include termination condition IDs if they exist
      const teamTerminationConditionIds = (teamResponse as any)
        .team_termination_condition_ids;
      if (
        Array.isArray(teamTerminationConditionIds) &&
        teamTerminationConditionIds.filter(Boolean).length > 0
      ) {
        payload.team_termination_condition_ids =
          teamTerminationConditionIds.filter(Boolean);
      }

      // Send the POST request to create duplicate
      const result = await createTeamMutation.mutateAsync(payload);

      toast({
        title: "Team Duplicated",
        description: `Team "${teamName}" has been successfully duplicated.`,
      });

      // Redirect to edit page of the new duplicated team
      navigate(`/team/edit/${result.id}`);
    } catch (error: any) {
      console.error("Failed to duplicate team:", error);
      toast({
        title: "Duplication Failed",
        description:
          error?.message ||
          "There was an error duplicating your team. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Clear loading state
      setDuplicatingTeamId(null);
    }
  };

  // Undeploy team function
  const handleUndeployTeam = async (teamId: string, teamName: string) => {
    // Close the dropdown first
    try {
      await undeployTeamMutation.mutateAsync(teamId);
      toast({
        title: "Team Undeployed",
        description: `Team "${teamName}" has been undeployed.`,
      });
      // Optionally, you can force a refetch or update local state if needed
    } catch (error: any) {
      toast({
        title: "Undeploy Failed",
        description:
          error?.message ||
          "There was an error undeploying your team. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Deploy team function
  const handleDeployTeam = async (team: Team2) => {
    // Close the dropdown first
    try {
      await deployTeamMutation.mutateAsync(team.id);
      toast({
        title: "Team Deployed",
        description: `Team "${team.name}" has been deployed.`,
      });
      // Optionally, you can force a refetch or update local state if needed
    } catch (error: any) {
      toast({
        title: "Deployment Failed",
        description:
          error?.message ||
          "There was an error deploying your team. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card
      key={team.id}
      className="hover:shadow-xl hover:shadow-slate-200/50 transition-all duration-300 bg-white border-slate-200 border overflow-hidden cursor-pointer group relative"
    >
      <CardContent className="flex h-full flex-col p-0">
        {/* Card Header with Actions */}
        <div className="flex-1 p-6 pb-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <h3 className="text-xl font-bold text-slate-900 truncate">
                {team.name}
              </h3>
            </div>
          </div>

          {/* Description */}
          <p className="text-slate-600 text-sm leading-relaxed line-clamp-2 mb-4">
            {team.description}
          </p>
        </div>
        <div>
          <div className="px-4">
            <Separator className="border-t" />
          </div>
          <div className="p-4 flex justify-between items-center">
            <div className="text-xs text-slate-500 font-bold">
              by {user.name || "User"}
            </div>
            <div className="flex items-center">
              {/* Undeploy button - only show if team is deployed */}
              {team.status === "Active" && (
                <>
                  <TooltipText text="Undeploy">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUndeployTeam(team.id, team.name);
                      }}
                      disabled={undeployTeamMutation.isPending}
                    >
                      {undeployTeamMutation.isPending ? (
                        <>
                          <Loader2 className="animate-spin" />
                        </>
                      ) : (
                        <>
                          <Play className="rotate-180 fill-yellow-600 stroke-yellow-500" />
                        </>
                      )}
                    </Button>
                  </TooltipText>
                  <TooltipText text="Go to Chat">
                    <Link to={`/workflows/${team.id}/chat/sessions`}>
                      <Button
                        variant="ghost"
                        size="icon"
                        disabled={undeployTeamMutation.isPending}
                      >
                        <ExternalLink />
                      </Button>
                    </Link>
                  </TooltipText>
                </>
              )}
              {team.status !== "Active" && (
                <TooltipText text="Deploy">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeployTeam(team)}
                    className="text-emerald-600 fill-emerald-500"
                  >
                    <Play className="fill-emerald-500 stroke-emerald-500" />
                  </Button>
                </TooltipText>
              )}
              <TooltipText text="Edit">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleEditTeam(team)}
                >
                  <Edit />
                </Button>
              </TooltipText>
              <TooltipText text="Duplicate">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDuplicateTeam(team.id, team.name);
                  }}
                  disabled={duplicatingTeamId === team.id}
                >
                  {duplicatingTeamId === team.id ? (
                    <>
                      <Loader2 className="animate-spin" />
                    </>
                  ) : (
                    <>
                      <Copy />
                    </>
                  )}
                </Button>
              </TooltipText>
              <TooltipText text="Delete">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-red-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteTeam(team);
                  }}
                >
                  <Trash2 />
                </Button>
              </TooltipText>
            </div>
          </div>
        </div>
        {/* Hover Overlay for Quick Actions */}
        <div className="absolute inset-0 bg-slate-900/5 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
      </CardContent>
    </Card>
  );
}
