import { Plus, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Link } from "react-router-dom";
import { useState } from "react";
import { BrowseTemplatesModal } from "@/components/templates/BrowseTemplatesModal";

export const WorkflowDropdown = () => {
  const [showBrowseTemplates, setShowBrowseTemplates] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const handleBrowseTemplatesClick = () => {
    setDropdownOpen(false); // Close dropdown first
    setShowBrowseTemplates(true);
  };

  return (
    <>
    <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
      <DropdownMenuTrigger asChild>
        <Button className="bg-teal-800 hover:bg-teal-700 text-white h-auto flex items-center gap-3 font-semibold transition-colors rounded-lg py-[10px] px-[16px] text-sm">
          <Plus className="h-5 w-5" />
          New Workflow
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem asChild>
          <Link to="/create" className="flex items-center gap-2 cursor-pointer">
            <Plus className="h-4 w-4" />
            Create from Scratch
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem 
          className="flex items-center gap-2 cursor-pointer"
          onClick={handleBrowseTemplatesClick}
        >
          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
          Browse Templates
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

      <BrowseTemplatesModal
        isOpen={showBrowseTemplates}
        onClose={() => setShowBrowseTemplates(false)}
      />
    </>
  );
};
