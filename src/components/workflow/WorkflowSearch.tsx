import { Filter, Loader2, Search } from "lucide-react";
import { Input } from "../ui/input";
import * as Select from "../ui/select";
import { useWorkflow } from "@/contexts/WorkflowContext";

type Props = {
  searchTerm: string;
  setSearchTerm: (val: string) => void;
  statusFilter: string;
  setStatusFilter: (val: string) => void;
  isSearching: boolean;
};

export default function WorkflowSearch() {
  const {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    isSearching,
  } = useWorkflow();

  return (
    <div className="flex gap-4 mb-8">
      <div className="relative flex-1">
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
        <Input
          placeholder="Search workflows by name or description..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-12 h-14 border-slate-300 focus:border-teal-500 focus:ring-teal-500 text-base"
        />
        {isSearching && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            <Loader2 className="h-5 w-5 text-slate-400 animate-spin" />
          </div>
        )}
      </div>
      <Select.Select value={statusFilter} onValueChange={setStatusFilter}>
        <Select.SelectTrigger className="w-fit h-14 border-slate-300 text-base flex gap-2">
          <Filter className="h-5 w-5" />
          <Select.SelectValue placeholder="Filter by status" />
        </Select.SelectTrigger>
        <Select.SelectContent>
          <Select.SelectItem value="all">All Workflows</Select.SelectItem>
          <Select.SelectItem value="active">Deployed</Select.SelectItem>
          <Select.SelectItem value="inactive">Undeployed</Select.SelectItem>
        </Select.SelectContent>
      </Select.Select>
    </div>
  );
}
