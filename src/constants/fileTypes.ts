import {
  FileText,
  FileCode,
  FileSpreadsheet,
  FileImage,
  File,
  FileX,
  Database,
  Globe,
  BookOpen,
  Code,
  Image,
  type LucideIcon
} from "lucide-react";

// Supported file types from backend
export const SUPPORTED_FILE_TYPES = [
  // Text files
  "text/plain",
  "text/markdown",
  "text/csv",
  "text/html",
  "text/css",
  "text/xml",
  "text/yaml",
  "text/x-python",
  "text/x-javascript",
  "text/typescript",
  "text/x-java",
  "text/x-c",
  "text/x-cpp",
  "text/x-csharp",
  "text/x-go",
  "text/x-rust",
  "text/x-php",
  "text/x-ruby",
  "text/x-swift",
  "text/x-kotlin",
  // Application files
  "application/json",
  "application/pdf",
  "application/xml",
  "application/yaml",
  "application/typescript",
  "application/javascript",
  // Microsoft Office
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  "application/msword",
  "application/vnd.ms-excel",
  "application/vnd.ms-powerpoint",
  // Images
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
  "image/svg+xml",
  "image/bmp",
  "image/tiff",
] as const;

// File type to icon mapping
export const FILE_TYPE_ICONS: Record<string, LucideIcon> = {
  // Text files
  "text/plain": FileText,
  "text/markdown": BookOpen,
  "text/csv": FileSpreadsheet,
  "text/html": Globe,
  "text/css": FileCode,
  "text/xml": FileCode,
  "text/yaml": FileCode,

  // Programming languages
  "text/x-python": FileCode,
  "text/x-javascript": FileCode,
  "text/typescript": FileCode,
  "application/typescript": FileCode,
  "application/javascript": FileCode,
  "text/x-java": FileCode,
  "text/x-c": FileCode,
  "text/x-cpp": FileCode,
  "text/x-csharp": FileCode,
  "text/x-go": FileCode,
  "text/x-rust": FileCode,
  "text/x-php": FileCode,
  "text/x-ruby": FileCode,
  "text/x-swift": FileCode,
  "text/x-kotlin": FileCode,

  // Application files
  "application/json": FileCode,
  "application/pdf": FileText,
  "application/xml": FileCode,
  "application/yaml": FileCode,

  // Microsoft Office - Word
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": FileText,
  "application/msword": FileText,

  // Microsoft Office - Excel
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": FileSpreadsheet,
  "application/vnd.ms-excel": FileSpreadsheet,

  // Microsoft Office - PowerPoint
  "application/vnd.openxmlformats-officedocument.presentationml.presentation": FileText,
  "application/vnd.ms-powerpoint": FileText,

  // Images
  "image/jpeg": FileImage,
  "image/png": FileImage,
  "image/gif": FileImage,
  "image/webp": FileImage,
  "image/svg+xml": FileImage,
  "image/bmp": FileImage,
  "image/tiff": FileImage,
};

// Helper function to get icon for file type
export const getFileTypeIcon = (mimeType: string): LucideIcon => {
  return FILE_TYPE_ICONS[mimeType] || File;
};

// Helper function to get file extension from mime type
export const getFileExtensionFromMimeType = (mimeType: string): string => {
  const extensionMap: Record<string, string> = {
    "text/plain": "txt",
    "text/markdown": "md",
    "text/csv": "csv",
    "text/html": "html",
    "text/css": "css",
    "text/xml": "xml",
    "text/yaml": "yaml",
    "text/x-python": "py",
    "text/x-javascript": "js",
    "text/typescript": "ts",
    "application/typescript": "ts",
    "application/javascript": "js",
    "text/x-java": "java",
    "text/x-c": "c",
    "text/x-cpp": "cpp",
    "text/x-csharp": "cs",
    "text/x-go": "go",
    "text/x-rust": "rs",
    "text/x-php": "php",
    "text/x-ruby": "rb",
    "text/x-swift": "swift",
    "text/x-kotlin": "kt",
    "application/json": "json",
    "application/pdf": "pdf",
    "application/xml": "xml",
    "application/yaml": "yaml",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
    "application/msword": "doc",
    "application/vnd.ms-excel": "xls",
    "application/vnd.ms-powerpoint": "ppt",
    "image/jpeg": "jpg",
    "image/png": "png",
    "image/gif": "gif",
    "image/webp": "webp",
    "image/svg+xml": "svg",
    "image/bmp": "bmp",
    "image/tiff": "tiff",
  };

  return extensionMap[mimeType] || "";
};

// Helper function to get mime type from file extension
export const getMimeTypeFromExtension = (extension: string): string => {
  const extensionToMimeType: Record<string, string> = {
    txt: "text/plain",
    md: "text/markdown",
    csv: "text/csv",
    html: "text/html",
    css: "text/css",
    xml: "text/xml",
    yaml: "text/yaml",
    yml: "text/yaml",
    py: "text/x-python",
    js: "text/x-javascript",
    ts: "text/typescript",
    java: "text/x-java",
    c: "text/x-c",
    cpp: "text/x-cpp",
    cs: "text/x-csharp",
    go: "text/x-go",
    rs: "text/x-rust",
    php: "text/x-php",
    rb: "text/x-ruby",
    swift: "text/x-swift",
    kt: "text/x-kotlin",
    json: "application/json",
    pdf: "application/pdf",
    docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    doc: "application/msword",
    xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    xls: "application/vnd.ms-excel",
    pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    ppt: "application/vnd.ms-powerpoint",
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    png: "image/png",
    gif: "image/gif",
    webp: "image/webp",
    svg: "image/svg+xml",
    bmp: "image/bmp",
    tiff: "image/tiff",
  };

  return extensionToMimeType[extension.toLowerCase()] || "";
};
