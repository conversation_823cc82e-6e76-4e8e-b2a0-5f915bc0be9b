import { ChatItem, ChatTypes } from '@/types/chat';
import ChatService from '@/services/chatService';
import { useToast } from '@/hooks/use-toast';
import React, { createContext, useContext, useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface ChatContextType {
  sessions: ChatTypes | null;
  setSessions: (sessions: ChatTypes | null) => void;
  fetchSessionsByTeam: (teamId: string) => Promise<void>;
  createSession: (teamId: string, name?: string) => Promise<ChatItem>;
  updateSession: (sessionId: string, name: string) => Promise<void>;
  deleteSession: (sessionId: string, currentSessionId?: string) => Promise<void>;
  isSessionFetched: boolean;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const useChatContext = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
};

interface ChatProviderProps {
  children: React.ReactNode;
}

const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [sessions, setSessions] = useState<ChatTypes | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isSessionFetched, setIsSessionFetched] = useState(false);

  const createSession = async (teamId: string, name: string = "New Chat"): Promise<ChatItem> => {
    try {
      const newSession = await ChatService.createSession({ name, team_id: teamId });

      setSessions(prevSessions => {
        if (!prevSessions) {
          return {
            items: [newSession],
            total: 1,
            page: 1,
            pages: 1,
            size: 1
          };
        }

        return {
          ...prevSessions,
          items: [newSession, ...prevSessions.items],
          total: prevSessions.total + 1
        };
      });
      return newSession;
    } catch (error) {
      console.error('Failed to create session:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create session",
        variant: "destructive",
      });
      throw error; // Re-throw to handle in deleteSession
    }
  };

  const fetchSessionsByTeam = async (teamId: string): Promise<void> => {
    try {
      setIsSessionFetched(false)
      const sessionsData = await ChatService.getSessionsByTeam(teamId);

      if (sessionsData.items.length === 0) {
        await createSession(teamId);
      } else {
        setSessions(sessionsData);
      } 
      setIsSessionFetched(true);
    } catch (error) {
      console.error('Failed to fetch sessions:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch sessions",
        variant: "destructive",
      });
    }
  };

  const updateSession = async (sessionId: string, name: string): Promise<void> => {
    try {
      const updatedSession = await ChatService.updateSession(sessionId, { name });

      setSessions(prevSessions => {
        if (!prevSessions) return prevSessions;

        return {
          ...prevSessions,
          items: prevSessions.items.map(session =>
            session.id === sessionId ? updatedSession : session
          )
        };
      });
    } catch (error) {
      console.error('Failed to update session:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update session",
        variant: "destructive",
      });
    }
  };

  const deleteSession = async (sessionId: string, currentSessionId?: string): Promise<void> => {
    try {
      await ChatService.deleteSession(sessionId);

      setSessions(prevSessions => {
        if (!prevSessions) return prevSessions;

        const remainingSessions = prevSessions.items.filter(session => session.id !== sessionId);
        const isLastSession = remainingSessions.length === 0;

        // If this is the last session, we'll create a new one
        if (isLastSession) {
          // Get team_id from the session being deleted
          const deletedSession = prevSessions.items.find(session => session.id === sessionId);
          if (deletedSession) {
            // Create new session asynchronously
            createSession(deletedSession.team_id).then((newSession) => {
              // Navigate to the new session
              navigate(`/workflows/${deletedSession.team_id}/chat/sessions/${newSession.id}`);
            }).catch((error) => {
              console.error('Failed to create replacement session:', error);
              // Navigate to team chat without specific session
              navigate(`/workflows/${deletedSession.team_id}/chat/sessions`);
            });
          }

          return {
            ...prevSessions,
            items: [],
            total: 0
          };
        }

        // If we're deleting the currently viewed session, navigate to another session
        if (currentSessionId === sessionId && remainingSessions.length > 0) {
          const nextSession = remainingSessions[0];
          navigate(`/workflows/${nextSession.team_id}/chat/sessions/${nextSession.id}`);
        }

        return {
          ...prevSessions,
          items: remainingSessions,
          total: prevSessions.total - 1
        };
      });
    } catch (error) {
      console.error('Failed to delete session:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete session",
        variant: "destructive",
      });
    }
  };

  const value: ChatContextType = {
    sessions,
    setSessions,
    fetchSessionsByTeam,
    createSession,
    updateSession,
    deleteSession,
    isSessionFetched,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

export default ChatProvider;
