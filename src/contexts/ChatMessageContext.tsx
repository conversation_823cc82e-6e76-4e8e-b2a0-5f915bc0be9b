import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useCallback,
} from "react";
import { useToast } from "@/hooks/use-toast";
import ChatMessageService, {
  MessageType,
  MessageItem,
} from "@/services/chatMessageService";
import { useAuth } from "./AuthContext";

interface ChatMessageContextType {
  messages: MessageType | null;
  setMessages: (messages: MessageType | null) => void;
  fetchMessagesBySession: (sessionId: string) => Promise<void>;
  isMessagesLoading: boolean;
  isMessageError: string | null;
  handleSendMessage: (sessionId: string, message: string) => Promise<void>;
  isSendingMessage: boolean;
  errorSendingMessage: string | null;
  scrollToBottom: () => void;
  scrollRef: React.RefObject<HTMLDivElement>;
  setInputConfig: (inputConfig: any) => void;
  inputConfig: any;
}

const ChatMessageContext = createContext<ChatMessageContextType | undefined>(
  undefined
);

export const useChatMessageContext = (): ChatMessageContextType => {
  const context = useContext(ChatMessageContext);
  if (!context) {
    throw new Error(
      "useChatMessageContext must be used within a ChatMessageProvider"
    );
  }
  return context;
};

interface ChatMessageProviderProps {
  children: React.ReactNode;
}

const ChatMessageProvider: React.FC<ChatMessageProviderProps> = ({
  children,
}) => {
  const [messages, setMessages] = useState<MessageType | null>(null);
  const [isMessagesLoading, setIsMessagesLoading] = useState(false);
  const [isMessageError, setIsMessageError] = useState<string | null>(null);
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const [errorSendingMessage, setErrorSendingMessage] = useState<string | null>(
    null
  );
  const { user } = useAuth();
  const { toast } = useToast();
  const [inputConfig, setInputConfig] = useState<any>(null);

  // Scroll functionality
  const scrollRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = useCallback(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, []);

  const fetchMessagesBySession = async (sessionId: string): Promise<void> => {
    try {
      setIsMessagesLoading(true);
      const messagesData = await ChatMessageService.getMessagesBySession(
        sessionId
      );
      setMessages(messagesData);
    } catch (error) {
      console.error("Failed to fetch messages:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to fetch messages",
        variant: "destructive",
      });
      setIsMessageError(
        error instanceof Error ? error.message : "Failed to fetch messages"
      );
    } finally {
      setIsMessagesLoading(false);
    }
  };

  // Helper function to transform team inputs based on component type
  const transformTeamInputs = (message: string) => {
    const input_config = inputConfig.map((input) => {
      if (input.provider === "autogen_core.io.ChatInput") {
        return { ...input, content: message };
      }

      if (input.provider === "autogen_core.io.URLInput") {
        try {
          const parsedContent = {
            ...input.content,
            headers: JSON.parse(input.content.headers),
          };
          return { ...input, content: parsedContent };
        } catch (error) {
          console.error("Failed to parse headers:", error);
          return input;
        }
      }

      return input;
    });

    if (messages.messages.length === 0) {
      return input_config;
    }

    return input_config.filter(
      (item) => item.provider === "autogen_core.io.ChatInput"
    );
  };

  // Helper function to add user message to state
  const addUserMessage = (sessionId: string, messageInput: any) => {
    setMessages((prev) => {
      if (!prev) return prev;
      const message: MessageItem = {
        id: null,
        run_id: null,
        session_id: sessionId,
        config: {
          source: "user",
          message_type: "user_message",
          content: messageInput,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString() || null,
        is_deleted: false,
      };
      return {
        ...prev,
        messages: [...prev.messages, message],
      };
    });
    // Scroll to bottom after adding user message
    requestAnimationFrame(() => {
      setTimeout(scrollToBottom, 50);
    });
  };
  console.log("DEBUG: inputConfig:", inputConfig);
  // Helper function to add system message to state
  const addSystemMessage = (runData: any, messageData: any) => {
    setMessages((prev) => {
      if (!prev) return prev;
      const message: MessageItem = {
        id: null,
        run_id: runData.id,
        session_id: runData.session_id,
        config: {
          source: "system",
          content: messageData,
          message_type: "text",
        },
        created_at: runData.created_at.toString(),
        updated_at: runData.updated_at?.toString() || null,
        is_deleted: false,
      };
      return {
        ...prev,
        messages: [...prev.messages, message],
      };
    });
    // Scroll to bottom after adding system message
    requestAnimationFrame(() => {
      setTimeout(scrollToBottom, 50);
    });
  };

  const handleSendMessage = async (
    sessionId: string,
    message: string
  ): Promise<void> => {
    try {
      const teamInputs = transformTeamInputs(message);

      // Add user message to state immediately
      addUserMessage(sessionId, teamInputs);

      // Set loading state
      setIsSendingMessage(true);

      // Get run data from backend
      const runData = await ChatMessageService.getRunId(sessionId, user.id);

      // Prepare payload for sending message
      console.log("DEBUG: Team inputs:", teamInputs);
      const payload = {
        source: "user",
        inputs: teamInputs,
      };

      // Send message and get response
      await ChatMessageService.sendMessage(runData.id, payload);

      // Poll for workflow status
      const pollWorkflowStatus = async () => {
        try {
          const statusResponse = await ChatMessageService.getWorkflowStatus(
            runData.id
          );

          if (statusResponse.workflow_status === "completed") {
            addSystemMessage(runData, statusResponse.result);
            setIsSendingMessage(false);
            return;
          } else if (statusResponse.workflow_status === "failed") {
            setErrorSendingMessage("Failed to send message");
            setIsSendingMessage(false);
            toast({
              title: "Error",
              description: "Workflow failed to complete",
              variant: "destructive",
            });
            return;
          }

          // Continue polling if status is not completed or failed
          setTimeout(pollWorkflowStatus, 1000);
        } catch (error) {
          console.error("Failed to get workflow status:", error);
          setErrorSendingMessage(
            error instanceof Error ? error.message : "Failed to send message"
          );
          setIsSendingMessage(false);
          toast({
            title: "Error",
            description:
              error instanceof Error
                ? error.message
                : "Failed to get workflow status",
            variant: "destructive",
          });
        }
      };

      // Start polling
      setTimeout(pollWorkflowStatus, 1000);
    } catch (error) {
      console.error("Failed to send message:", error);
      setErrorSendingMessage(
        error instanceof Error ? error.message : "Failed to send message"
      );
      setIsSendingMessage(false);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to send message",
        variant: "destructive",
      });
    }
  };

  const value: ChatMessageContextType = {
    messages,
    setMessages,
    fetchMessagesBySession,
    isMessagesLoading,
    isMessageError,
    handleSendMessage,
    isSendingMessage,
    errorSendingMessage,
    scrollToBottom,
    scrollRef,
    setInputConfig,
    inputConfig,
  };

  return (
    <ChatMessageContext.Provider value={value}>
      {children}
    </ChatMessageContext.Provider>
  );
};

export default ChatMessageProvider;
