import React, { createContext, useContext, useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { envVarAPI } from "@/api/settings";
import {
  EnvironmentVariableCreate,
  EnvironmentVariableUpdate,
  EnvironmentVariableOut,
  PaginatedEnvVarResponse,
} from "@/types/settings";

interface EnvVarContextType {
  envVars: EnvironmentVariableOut[];
  total: number;
  page: number;
  pages: number;
  pageSize: number;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
  createEnvVar: (
    data: Omit<EnvironmentVariableCreate, "user_id">
  ) => Promise<EnvironmentVariableOut>;
  updateEnvVar: (
    id: string,
    data: EnvironmentVariableUpdate
  ) => Promise<EnvironmentVariableOut>;
  deleteEnvVar: (id: string) => Promise<void>;
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  search: string;
  setSearch: (search: string) => void;
}

const EnvVarContext = createContext<EnvVarContextType | undefined>(undefined);

export const useEnvVars = (): EnvVarContextType => {
  const context = useContext(EnvVarContext);
  if (context === undefined) {
    throw new Error("useEnvVars must be used within an EnvVarProvider");
  }
  return context;
};

interface EnvVarProviderProps {
  children: React.ReactNode;
}

export const EnvVarProvider: React.FC<EnvVarProviderProps> = ({ children }) => {
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(8);
  const [search, setSearch] = useState("");

  // Query for environment variables (paginated)
  const {
    data: envVarsData,
    isLoading,
    error,
    refetch,
  } = useQuery<PaginatedEnvVarResponse>({
    queryKey: ["envVars", page, pageSize, search],
    queryFn: () =>
      envVarAPI.getEnvVars({
        skip: (page - 1) * pageSize,
        limit: pageSize,
        name_like: search,
      }),
  });

  // Create
  const createMutation = useMutation({
    mutationFn: (data: Omit<EnvironmentVariableCreate, "user_id">) =>
      envVarAPI.createEnvVar(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["envVars"] });
    },
  });

  // Update
  const updateMutation = useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: EnvironmentVariableUpdate;
    }) => envVarAPI.updateEnvVar(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["envVars"] });
    },
  });

  // Delete
  const deleteMutation = useMutation({
    mutationFn: (id: string) => envVarAPI.deleteEnvVar(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["envVars"] });
    },
  });

  const value = useMemo<EnvVarContextType>(
    () => ({
      envVars: envVarsData?.items || [],
      total: envVarsData?.total || 0,
      page: envVarsData?.page || page,
      pages: envVarsData?.pages || 1,
      pageSize: envVarsData?.size || pageSize,
      isLoading,
      error: error as Error | null,
      refetch,
      createEnvVar: createMutation.mutateAsync,
      updateEnvVar: (id, data) => updateMutation.mutateAsync({ id, data }),
      deleteEnvVar: async (id) => {
        await deleteMutation.mutateAsync(id);
      },
      setPage,
      setPageSize,
      search,
      setSearch,
    }),
    [
      envVarsData,
      isLoading,
      error,
      refetch,
      createMutation,
      updateMutation,
      deleteMutation,
      page,
      pageSize,
      search,
    ]
  );

  return (
    <EnvVarContext.Provider value={value}>{children}</EnvVarContext.Provider>
  );
};
