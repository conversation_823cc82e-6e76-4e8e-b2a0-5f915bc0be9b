import React, { createContext, useContext, useState } from "react";
import { useTools, useDeleteTool } from "../hooks/useTools";
import { useDebounce } from "../hooks/useDebounce";
import { Tool2, ToolFilters } from "../types/tool";
import { useEnvironmentVariables } from "@/hooks/use-environment-variables";
import { EnvironmentVariableOut } from "@/types/settings";

interface ToolsContextType {
  // Data
  tools: Tool2[];
  totalTools: number;
  isLoading: boolean;
  error: Error | null;
  env_vars: EnvironmentVariableOut[];

  // Search and filters
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  isSearching: boolean;

  // Pagination
  currentPage: number;
  setCurrentPage: (page: number) => void;
  totalPages: number;
  isPaginating: boolean;

  // Actions
  deleteTool: (toolId: string) => Promise<void>;
  refetchTools: () => void;

  // Cache management
  isCached: boolean;
}

const ToolsContext = createContext<ToolsContextType | undefined>(undefined);

interface ToolsProviderProps {
  children: React.ReactNode;
}

export const ToolsProvider: React.FC<ToolsProviderProps> = ({ children }) => {
  // Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const itemsPerPage = 9;

  // Build API filters
  const apiFilters: ToolFilters = {
    skip: (currentPage - 1) * itemsPerPage,
    limit: itemsPerPage,
    sort_by: "updated_at",
    sort_order: "desc",
  };

  // Add search filter if present
  if (debouncedSearchTerm.trim()) {
    apiFilters.name_like = debouncedSearchTerm.trim();
  }

  // Fetch tools from backend
  const {
    data: toolsResponse,
    isLoading,
    error,
    isFetching,
    refetch,
  } = useTools(apiFilters);

  // Fetch environment variables
  const { data: env_vars = [] } = useEnvironmentVariables();

  const tools = React.useMemo(() => {
    return toolsResponse?.items || [];
  }, [toolsResponse]);

  const totalTools = React.useMemo(() => {
    return toolsResponse?.total || 0;
  }, [toolsResponse]);

  const totalPages = Math.ceil(totalTools / itemsPerPage);

  // Track search and pagination loading state
  const [isSearching, setIsSearching] = React.useState(false);
  const [isPaginating, setIsPaginating] = React.useState(false);

  // Show loading when search term changes (debounced search is happening)
  React.useEffect(() => {
    if (searchTerm !== debouncedSearchTerm) {
      setIsSearching(true);
    } else {
      setIsSearching(false);
    }
  }, [searchTerm, debouncedSearchTerm]);

  // Show loading when search is happening and we're fetching
  React.useEffect(() => {
    if (isFetching && debouncedSearchTerm.trim() !== "") {
      setIsSearching(true);
    } else if (!isFetching) {
      setIsSearching(false);
    }
  }, [isFetching, debouncedSearchTerm]);

  // Show loading when pagination changes and we're fetching
  React.useEffect(() => {
    if (isFetching && !isSearching) {
      setIsPaginating(true);
    } else {
      setIsPaginating(false);
    }
  }, [isFetching, isSearching]);

  // Delete tool mutation
  const deleteToolMutation = useDeleteTool();

  const deleteTool = async (toolId: string) => {
    try {
      await deleteToolMutation.mutateAsync({ id: toolId, permanent: false });
      // Refetch tools after successful deletion
      refetch();
    } catch (error) {
      console.error("Failed to delete tool:", error);
      throw error;
    }
  };

  // Check if data is cached (has been fetched before)
  const isCached = React.useMemo(() => {
    return !!toolsResponse && !isLoading;
  }, [toolsResponse, isLoading]);

  const value: ToolsContextType = {
    // Data
    tools,
    totalTools,
    isLoading,
    error,
    env_vars,

    // Search and filters
    searchTerm,
    setSearchTerm,
    isSearching,

    // Pagination
    currentPage,
    setCurrentPage,
    totalPages,
    isPaginating,

    // Actions
    deleteTool,
    refetchTools: refetch,

    // Cache management
    isCached,
  };

  return (
    <ToolsContext.Provider value={value}>{children}</ToolsContext.Provider>
  );
};

export const useToolsContext = (): ToolsContextType => {
  const context = useContext(ToolsContext);
  if (context === undefined) {
    throw new Error("useToolsContext must be used within a ToolsProvider");
  }
  return context;
};
