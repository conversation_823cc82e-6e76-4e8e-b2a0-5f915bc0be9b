import { useFlag, useFlagsStatus, IMutableContext } from "@unleash/proxy-client-react";
import { createContext } from "react";

export interface IFeatureFlagContextProps {
  enableGoogleLogin?: boolean;
  enableGitHubLogin?: boolean;
}

const initialValues = {
  enableGoogleLogin: true,
  enableGitHubLogin: true,
};

const context: IMutableContext = {
  properties: {
    instance_id: import.meta.env.VITE_UNLEASH_INSTANCE_ID,
    user_id: import.meta.env.VITE_UNLEASH_USER_ID,
    domain: import.meta.env.VITE_UNLEASH_DOMAIN,
  },
};
export const config = {
  url: import.meta.env.VITE_UNLEASH_CONNECTING_URL, // Your local instance Unleash API URL
  clientKey: import.meta.env.VITE_UNLEASH_FRONTEND_API_KEY, // Your client-side API token
  refreshInterval: 15000, // How often (in seconds) the client should poll the proxy for updates
  appName: import.meta.env.VITE_UNLEASH_APP_NAME, // The name of your application. It's only used for identifying your application
  environment: import.meta.env.VITE_ENVIRONMENT || "development", // Specify the environment
  context: context,
};

export const FeatureFlagContext = createContext<IFeatureFlagContextProps>(initialValues);

const FeatureFlagProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { flagsError } = useFlagsStatus();
  const enableGoogleLogin = flagsError ? true : useFlag("enableGoogleLogin");
  const enableGitHubLogin = flagsError ? true : useFlag("enableGithubLogin");

  return (
    <FeatureFlagContext.Provider
      value={{
        enableGoogleLogin,
        enableGitHubLogin,
      }}
    >
      {children}
    </FeatureFlagContext.Provider>
  );
};

export default FeatureFlagProvider;
