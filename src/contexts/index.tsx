import { FlagProvider } from "@unleash/proxy-client-react";
import FeatureFlagProvider, { config } from "./featureFlagContext";
import { ReactNode } from "react";


export default function ContextWrapper({ children }: { children: ReactNode }) {
  //element to wrap all context
  return (
    <>
      <FlagProvider config={config}>
        <FeatureFlagProvider>
            {children}
        </FeatureFlagProvider>
      </FlagProvider>
    </>
  );
}
