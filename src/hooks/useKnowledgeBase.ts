import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { knowledgeBaseAPI } from "@/api/knowledge-base";
import {
  KnowledgeBase,
  KnowledgeBaseCreate,
  KnowledgeBaseUpdate,
  KnowledgeBaseFilters,
  BulkUploadResponse,
  KnowledgeBaseFilesResponse,
} from "@/types/knowledge-base";
import { useToast } from "./use-toast";

// Query keys
const QUERY_KEYS = {
  knowledgeBases: "knowledgeBases",
  knowledgeBase: "knowledgeBase",
  knowledgeBaseFiles: "knowledgeBaseFiles",
};

// Hook to get all knowledge bases
export const useKnowledgeBases = (filters?: KnowledgeBaseFilters) => {
  return useQuery({
    queryKey: [QUERY_KEYS.knowledgeBases, filters],
    queryFn: () => knowledgeBaseAPI.getKnowledgeBases(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to get a specific knowledge base
export const useKnowledgeBase = (id: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.knowledgeBase, id],
    queryFn: () => knowledgeBaseAPI.getKnowledgeBase(id),
    enabled: !!id,
  });
};

// Hook to get files in a knowledge base
export const useKnowledgeBaseFiles = (folderId: string) => {
  return useQuery<KnowledgeBaseFilesResponse>({
    queryKey: [QUERY_KEYS.knowledgeBaseFiles, folderId],
    queryFn: () => knowledgeBaseAPI.getKnowledgeBaseFiles(folderId),
    enabled: !!folderId,
  });
};

// Hook to create a knowledge base
export const useCreateKnowledgeBase = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: KnowledgeBaseCreate) => knowledgeBaseAPI.createKnowledgeBase(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.knowledgeBases] });
      toast({
        title: "Success",
        description: `Knowledge base "${data.folder_name}" created successfully with ${data.successful_uploads} files uploaded.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create knowledge base",
        variant: "destructive",
      });
    },
  });
};

// Hook to update a knowledge base
export const useUpdateKnowledgeBase = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: KnowledgeBaseUpdate }) =>
      knowledgeBaseAPI.updateKnowledgeBase(id, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.knowledgeBases] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.knowledgeBase, variables.id] });
      toast({
        title: "Success",
        description: "Knowledge base updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update knowledge base",
        variant: "destructive",
      });
    },
  });
};

// Hook to delete a knowledge base
export const useDeleteKnowledgeBase = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, permanent = false }: { id: string; permanent?: boolean }) =>
      knowledgeBaseAPI.deleteKnowledgeBase(id, permanent),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.knowledgeBases] });
      queryClient.removeQueries({ queryKey: [QUERY_KEYS.knowledgeBase, variables.id] });
      toast({
        title: "Success",
        description: variables.permanent
          ? "Knowledge base permanently deleted"
          : "Knowledge base deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete knowledge base",
        variant: "destructive",
      });
    },
  });
};

// Hook to restore a knowledge base
export const useRestoreKnowledgeBase = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => knowledgeBaseAPI.restoreKnowledgeBase(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.knowledgeBases] });
      queryClient.setQueryData([QUERY_KEYS.knowledgeBase, data.id], data);
      toast({
        title: "Success",
        description: "Knowledge base restored successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to restore knowledge base",
        variant: "destructive",
      });
    },
  });
};

// Hook to download a file
export const useDownloadFile = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (fileId: string) => knowledgeBaseAPI.downloadFile(fileId),
    onSuccess: (blob, fileId) => {
      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `file_${fileId}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Success",
        description: "File downloaded successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to download file",
        variant: "destructive",
      });
    },
  });
};

// Hook to generate presigned URL
export const useGeneratePresignedUrl = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (fileId: string) => knowledgeBaseAPI.generatePresignedUrl(fileId),
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to generate presigned URL",
        variant: "destructive",
      });
    },
  });
};

// Hook to delete a file
export const useDeleteFile = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (fileId: string) => knowledgeBaseAPI.deleteFile(fileId),
    onSuccess: (data, fileId) => {
      // Invalidate files queries to refresh the file list
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.knowledgeBaseFiles] });
      toast({
        title: "Success",
        description: data.message || "File deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete file",
        variant: "destructive",
      });
    },
  });
};

// Hook to bulk upload files to existing knowledge base
export const useBulkUploadFiles = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ folderId, files }: { folderId: string; files: File[] }) =>
      knowledgeBaseAPI.bulkUploadFiles(folderId, files),
    onSuccess: (data: BulkUploadResponse, variables) => {
      // Invalidate files queries to refresh the file list
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.knowledgeBaseFiles, variables.folderId] });
      toast({
        title: "Success",
        description: `Successfully uploaded ${data.successful_uploads} out of ${data.total_files} files.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to upload files",
        variant: "destructive",
      });
    },
  });
};
