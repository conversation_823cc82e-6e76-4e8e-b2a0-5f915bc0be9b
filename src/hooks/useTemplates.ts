import {
  useQuery,
  useMutation,
  useQueryClient,
  keepPreviousData,
} from "@tanstack/react-query";
import { templatesApi } from "../api/templates";
import {
  TemplateFilters,
  TemplateCreateRequest,
  TemplateUpdate,
  TemplateResponse,
  TemplateInstantiationRequest,
} from "../types/template";

// Get all templates with filtering and pagination
export const useTemplates = (params?: TemplateFilters) => {
  return useQuery({
    queryKey: ["templates", params],
    queryFn: () => templatesApi.getAll(params),
    placeholderData: keepPreviousData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

// Get template by ID
export const useTemplate = (id: string) => {
  return useQuery({
    queryKey: ["template", id],
    queryFn: () => templatesApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Create template
export const useCreateTemplate = (onTemplateCreated?: (template: TemplateResponse) => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: TemplateCreateRequest) => templatesApi.create(data),
    onSuccess: (newTemplate) => {
      if (onTemplateCreated) {
        onTemplateCreated(newTemplate);
      } else {
        queryClient.invalidateQueries({ queryKey: ["templates"] });
      }
    },
  });
};

// Update template
export const useUpdateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: TemplateUpdate }) =>
      templatesApi.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
      queryClient.invalidateQueries({ queryKey: ["template", id] });
    },
  });
};

// Delete template
export const useDeleteTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => templatesApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
    },
  });
};

// Instantiate template as workflow
export const useInstantiateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: TemplateInstantiationRequest) => templatesApi.instantiate(data),
    onSuccess: () => {
      // Invalidate teams query since a new team was created
      queryClient.invalidateQueries({ queryKey: ["teams"] });
    },
  });
};