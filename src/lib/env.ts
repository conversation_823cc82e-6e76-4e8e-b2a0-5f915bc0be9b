import { z } from "zod";

const schema = z.object({
  backend_url: z.string().url(),
  websocket_url: z.string().url(),
  cf_turnstile_sitekey: z.string(),

  sentry_url: z.string().url().optional(),
  // Unleash Feature Flags
  unleash_connecting_url: z.string().url(),
  unleash_frontend_api_key: z.string(),
  unleash_app_name: z.string(),
  unleash_domain: z.string(),
  unleash_instance_id: z.string(),
  unleash_user_id: z.string(),

  environment: z.string().default("development")
});

type Env = z.infer<typeof schema>;

// `process.env` for server variables
// `import.meta.env` for client variables (need to be prefixed by `VITE_`)

export function get_client_env(): Env {
  return schema.parse({
    backend_url: import.meta.env.VITE_BACKEND_URL,
    websocket_url: import.meta.env.VITE_WEBSOCKET_URL,
    cf_turnstile_sitekey: import.meta.env.VITE_CF_TURNSTILE_SITEKEY,

    sentry_url: import.meta.env.VITE_SENTRY_URL,
    
    // Unleash Feature Flags
    unleash_connecting_url: import.meta.env.VITE_UNLEASH_CONNECTING_URL,
    unleash_frontend_api_key: import.meta.env.VITE_UNLEASH_FRONTEND_API_KEY,
    unleash_app_name: import.meta.env.VITE_UNLEASH_APP_NAME,
    unleash_domain: import.meta.env.VITE_UNLEASH_DOMAIN,
    unleash_instance_id: import.meta.env.VITE_UNLEASH_INSTANCE_ID,
    unleash_user_id: import.meta.env.VITE_UNLEASH_USER_ID,

    environment: import.meta.env.VITE_ENVIRONMENT,
  });
}

export function get_server_env(): Env {
  return schema.parse({
    backend_url: process.env.BACKEND_URL,
    websocket_url: process.env.WEBSOCKET_URL,
    cf_turnstile_sitekey: process.env.CF_TURNSTILE_SITEKEY,

    sentry_url: process.env.SENTRY_URL,
    
    // Unleash Feature Flags
    unleash_connecting_url: process.env.UNLEASH_CONNECTING_URL,
    unleash_frontend_api_key: process.env.UNLEASH_FRONTEND_API_KEY,
    unleash_app_name: process.env.UNLEASH_APP_NAME,
    unleash_domain: process.env.UNLEASH_DOMAIN,
    unleash_instance_id: process.env.UNLEASH_INSTANCE_ID,
    unleash_user_id: process.env.UNLEASH_USER_ID,

    environment: process.env.ENVIRONMENT,
  });
}
