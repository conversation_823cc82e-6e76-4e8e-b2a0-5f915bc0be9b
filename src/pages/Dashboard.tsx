import { useState } from "react";
import { Plus, Search, Play, Users } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Link } from "react-router-dom";
import { Team2 } from "../types/team";
import { useWorkflow } from "@/contexts/WorkflowContext";
import { SkeletonGrid } from "@/components/ui/skeleton-card";
import { DeleteWorkflowModal } from "@/components/modals/DeleteWorkflowModal";
import { WorkflowDropdown } from "@/components/workflow/WorkflowDropdown";
import { TemplatesSection } from "@/components/templates/TemplatesSection";
import WorkflowCard from "@/components/workflow/WorkflowCard";
import WorkflowSearch from "@/components/workflow/WorkflowSearch";

const Dashboard = () => {
  // Use WorkflowContext for all team data and state management
  const {
    teams,
    activeWorkflow,
    totalWorkflow,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    isSearching,
    currentPage,
    setCurrentPage,
    totalPages,
    isPaginating,
    deleteTeam: contextDeleteTeam,
  } = useWorkflow();

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [workflowToDelete, setWorkflowToDelete] = useState<Team2 | null>(null);

  // Actual deletion function called by the modal
  const performDeleteTeam = async (teamId: string, teamName: string) => {
    return await contextDeleteTeam(teamId, teamName);
  };

  // Open delete confirmation modal
  const handleDeleteTeam = (team: Team2) => {
    // Small delay to allow dropdown to close before opening modal
    setTimeout(() => {
      setWorkflowToDelete(team);
      setIsDeleteModalOpen(true);
    }, 100);
  };

  // Close delete modal
  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setWorkflowToDelete(null);
    // Ensure dropdown is closed
  };

  // Error state
  if (error) {
    return (
      <div className="p-8 max-w-7xl mx-auto">
        <div className="text-center py-20">
          <div className="text-red-500 text-xl font-semibold mb-3">
            Error loading teams
          </div>
          <p className="text-slate-400 mb-8 text-base">
            Please try refreshing the page
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-7xl mx-auto">
      {/* Header with Stats - Only show when workflows exist or when loading/searching */}
      {(teams.length > 0 ||
        isLoading ||
        isSearching ||
        isPaginating ||
        searchTerm.trim() !== "" ||
        statusFilter !== "all") && (
        <>
          <div className="mb-8">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h1 className="text-3xl font-bold text-slate-900 mb-2">
                  Workflows
                </h1>
                <p className="text-slate-600 text-lg">
                  Manage and deploy your intelligent workflows
                </p>
              </div>
              <WorkflowDropdown />
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <Card className="bg-white border-slate-200 border-2">
                <CardContent className="p-8">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-slate-700 text-sm font-semibold mb-2">
                        Total Workflows
                      </p>
                      <p className="text-4xl font-bold text-slate-900">
                        {totalWorkflow}
                      </p>
                    </div>
                    <div className="w-14 h-14 bg-slate-100 rounded-xl flex items-center justify-center border-2 border-slate-200">
                      <Users className="h-7 w-7 text-slate-700" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-slate-200 border-2">
                <CardContent className="p-8">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-slate-700 text-sm font-semibold mb-2">
                        Active Workflows
                      </p>
                      <p className="text-4xl font-bold text-slate-900">
                        {activeWorkflow}
                      </p>
                    </div>
                    <div className="w-14 h-14 bg-slate-100 rounded-xl flex items-center justify-center border-2 border-slate-200">
                      <Play className="h-7 w-7 text-slate-700" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Enhanced Filters */}
          <WorkflowSearch />
        </>
      )}

      {/* New Workflow Button for Empty State */}
      {teams.length === 0 &&
        !isLoading &&
        !isSearching &&
        !isPaginating &&
        searchTerm.trim() === "" &&
        statusFilter === "all" && (
          <div className="flex justify-end mb-8">
            <WorkflowDropdown />
          </div>
        )}

      {/* Enhanced Team Cards */}
      {isLoading || isSearching || isPaginating ? (
        <SkeletonGrid count={9} />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {teams.map((team) => (
            <WorkflowCard
              key={team.id}
              handleDeleteTeam={handleDeleteTeam}
              team={team}
            />
          ))}
        </div>
      )}

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-8">
          <div className="text-sm text-slate-600">
            {/* Showing {teams.length} of {totalTeams} workflows */}
          </div>

          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => setCurrentPage(currentPage - 1)}
                  className={
                    currentPage === 1
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>

              {/* Show page numbers with ellipsis for large page counts */}
              {totalPages <= 7 ? (
                // Show all pages if 7 or fewer
                Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCurrentPage(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  )
                )
              ) : (
                // Show ellipsis for large page counts
                <>
                  {currentPage <= 4 ? (
                    <>
                      {Array.from({ length: 5 }, (_, i) => i + 1).map(
                        (page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => setCurrentPage(page)}
                              isActive={currentPage === page}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      )}
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(totalPages)}
                          className="cursor-pointer"
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  ) : currentPage >= totalPages - 3 ? (
                    <>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(1)}
                          className="cursor-pointer"
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      {Array.from(
                        { length: 5 },
                        (_, i) => totalPages - 4 + i
                      ).map((page) => (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => setCurrentPage(page)}
                            isActive={currentPage === page}
                            className="cursor-pointer"
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                    </>
                  ) : (
                    <>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(1)}
                          className="cursor-pointer"
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      {Array.from(
                        { length: 3 },
                        (_, i) => currentPage - 1 + i
                      ).map((page) => (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => setCurrentPage(page)}
                            isActive={currentPage === page}
                            className="cursor-pointer"
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(totalPages)}
                          className="cursor-pointer"
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}
                </>
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() => setCurrentPage(currentPage + 1)}
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {/* No Search Results State */}
      {teams.length === 0 &&
        !isLoading &&
        !isSearching &&
        !isPaginating &&
        (searchTerm.trim() !== "" || statusFilter !== "all") && (
          <div className="text-center py-20">
            <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-8 border-2 border-slate-200">
              <Search className="h-12 w-12 text-slate-400" />
            </div>
            <div className="text-slate-500 text-xl font-semibold mb-3">
              No workflows found for "{searchTerm.trim() || "your search"}"
            </div>
            <p className="text-slate-400 mb-8 text-base">
              Try adjusting your search terms or filters to find what you're
              looking for
            </p>
            <div className="flex gap-4 justify-center">
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("all");
                }}
                className="border-slate-300 text-slate-600 hover:bg-slate-50"
              >
                Clear Search
              </Button>
              <Link to="/create">
                <Button className="bg-teal-800 hover:bg-teal-700 text-white">
                  <Plus className="h-5 w-5 mr-2" />
                  Create New Workflow
                </Button>
              </Link>
            </div>
          </div>
        )}

      {/* General Empty State (when no search/filter is active) */}
      {teams.length === 0 &&
        !isLoading &&
        !isSearching &&
        !isPaginating &&
        searchTerm.trim() === "" &&
        statusFilter === "all" && <TemplatesSection />}

      {/* Delete Confirmation Modal */}
      <DeleteWorkflowModal
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        workflow={workflowToDelete}
        onDelete={performDeleteTeam}
      />
    </div>
  );
};

export default Dashboard;
