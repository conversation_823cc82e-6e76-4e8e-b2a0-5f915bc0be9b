import React, { useState } from "react";
import { Plus, Search, MoreVertical, Trash2, Edit, Download, Eye, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CreateKnowledgeBasePage } from "@/components/knowledge-base/create";
import { EditKnowledgeBasePage } from "@/components/knowledge-base/edit";
import { useKnowledgeBases, useDeleteKnowledgeBase, useDownloadFile } from "@/hooks/useKnowledgeBase";
import { KnowledgeBase } from "@/types/knowledge-base";
import { formatRelativeTime } from "@/lib/datetime";

const KnowledgeBaseManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreatePage, setShowCreatePage] = useState(false);
  const [showEditPage, setShowEditPage] = useState(false);
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<KnowledgeBase | null>(null);

  // API hooks
  const {
    data: knowledgeBasesData,
    isLoading,
    error,
  } = useKnowledgeBases({
    name_like: searchTerm || undefined,
    limit: 50,
  });
  const deleteKnowledgeBase = useDeleteKnowledgeBase();
  const downloadFile = useDownloadFile();

  const knowledgeBases = knowledgeBasesData?.items || [];

  const handleEdit = (knowledgeBase: KnowledgeBase) => {
    setSelectedKnowledgeBase(knowledgeBase);
    setShowEditPage(true);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this knowledge base?")) {
      await deleteKnowledgeBase.mutateAsync({ id });
    }
  };

  const handleBackToList = () => {
    setShowCreatePage(false);
    setShowEditPage(false);
    setSelectedKnowledgeBase(null);
  };

  // If showing create page, render that instead of the list
  if (showCreatePage) {
    return <CreateKnowledgeBasePage onBack={handleBackToList} knowledgeBase={null} />;
  }

  // If showing edit page, render that instead of the list
  if (showEditPage && selectedKnowledgeBase) {
    return <EditKnowledgeBasePage knowledgeBaseId={selectedKnowledgeBase.id} onBack={handleBackToList} />;
  }

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 mb-2">Knowledge Base</h1>
            <p className="text-slate-600 text-lg">Manage your document repositories and embedding configurations</p>
          </div>
          <Button
            onClick={() => {
              setSelectedKnowledgeBase(null);
              setShowCreatePage(true);
            }}
            className="bg-teal-800 hover:bg-teal-700 text-white h-auto flex items-center gap-3 font-semibold transition-colors rounded-lg py-[10px] px-[16px] text-sm"
          >
            <Plus className="h-5 w-5" />
            Create New
          </Button>
        </div>

        {/* Search */}
        <div className="flex gap-4 mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
            <Input
              placeholder="Search KBs"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Knowledge Base Repositories</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
              <span className="ml-2 text-slate-600">Loading knowledge bases...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500 text-lg mb-2">Failed to load knowledge bases</p>
              <p className="text-slate-400 text-sm">Please try refreshing the page</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Index Name</TableHead>
                  <TableHead>Last updated</TableHead>
                  <TableHead>No. of Files Added</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {knowledgeBases.map((knowledgeBase) => (
                  <TableRow
                    key={knowledgeBase.id}
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleEdit(knowledgeBase)}
                  >
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">{knowledgeBase.name}</div>
                    </TableCell>
                    <TableCell>
                      {knowledgeBase.updated_at
                        ? formatRelativeTime(knowledgeBase.updated_at)
                        : formatRelativeTime(knowledgeBase.created_at)}
                    </TableCell>
                    <TableCell>{knowledgeBase.file_count}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row click when delete is clicked
                          handleDelete(knowledgeBase.id);
                        }}
                        className="text-red-600"
                        disabled={deleteKnowledgeBase.isPending}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {!isLoading && !error && knowledgeBases.length === 0 && (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-6 mx-auto">
                <Search className="w-10 h-10 text-slate-400" />
              </div>
              <h3 className="text-lg font-semibold text-slate-900 mb-2">No Knowledge Bases Found</h3>
              <p className="text-slate-600 mb-6">
                {searchTerm ? "No results match your search." : "Create your first knowledge base to get started."}
              </p>
              {!searchTerm && (
                <Button onClick={() => setShowCreatePage(true)} className="bg-teal-800 hover:bg-teal-700 text-white">
                  <Plus className="mr-2 h-4 w-4" />
                  Create New Knowledge Base
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default KnowledgeBaseManagement;
