import React from "react";
import { Plus, Filter, Edit, Trash2, MoreVertical } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ToolConfigurationDrawer } from "@/components/drawers/ToolConfigurationDrawer";
import { Tool2 } from "@/types/tool";
import { ToolGrid, ToolSearch, ToolPagination } from "@/components/tools";
import { ToolsProvider, useToolsContext } from "@/contexts/ToolsContext";

const ToolsManagementContent = () => {
  const {
    tools,
    totalTools,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    isSearching,
    currentPage,
    setCurrentPage,
    totalPages,
    isPaginating,
    deleteTool,
    refetchTools,
  } = useToolsContext();

  const [statusFilter, setStatusFilter] = React.useState("all");
  const [selectedTool, setSelectedTool] = React.useState<Tool2 | null>(null);
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [isForking, setIsForking] = React.useState(false); // Track if we're forking a tool
  const [deletingToolId, setDeletingToolId] = React.useState<string | null>(
    null
  );

  // Client-side filtering for status (since backend filtering is limited)
  const filteredTools = React.useMemo(() => {
    return tools.filter((tool) => {
      const matchesStatus =
        statusFilter === "all" || tool.status.toLowerCase() === statusFilter;
      return matchesStatus;
    });
  }, [tools, statusFilter]);

  const getStatusColor = (status: string) => {
    return status === "Active"
      ? "bg-green-50 text-green-700 border-green-200"
      : "bg-gray-50 text-gray-700 border-gray-200";
  };

  const handleCardClick = (tool: Tool2) => {
    // Only allow editing for non-default tools
    if (!tool.is_default) {
      setSelectedTool(tool);
      setIsForking(false);
      setDrawerOpen(true);
    }
  };

  const handleFork = (tool: Tool2) => {
    // For default tools, open the drawer in fork mode (create mode with pre-filled data)
    setSelectedTool(tool);
    setIsForking(true);
    setDrawerOpen(true);
  };

  const handleDelete = async (toolId: string) => {
    setDeletingToolId(toolId);
    try {
      await deleteTool(toolId);
    } finally {
      setDeletingToolId(null);
    }
  };

  const handleAddNew = () => {
    setSelectedTool(null);
    setIsForking(false);
    setDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setDrawerOpen(false);
    setSelectedTool(null);
    setIsForking(false);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="p-8 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-20">
          <span className="ml-2 text-slate-600">Loading tools...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-8 max-w-7xl mx-auto">
        <div className="text-center py-20">
          <div className="text-red-600 text-xl font-semibold mb-3">
            Error loading tools
          </div>
          <p className="text-slate-400 mb-8 text-base">
            {error.message || "Something went wrong"}
          </p>
          <Button
            onClick={() => refetchTools()}
            className="bg-teal-800 hover:bg-teal-700 text-white px-8 py-4 h-auto rounded-lg font-semibold text-base"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 mb-2">Tools</h1>
            <p className="text-slate-600 text-lg">
              Manage and configure your automation tools
            </p>
          </div>
          <Button
            onClick={handleAddNew}
            className="bg-teal-800 hover:bg-teal-700 text-white h-auto flex items-center gap-3 font-semibold transition-colors rounded-lg py-[10px] px-[16px] text-sm"
          >
            <Plus className="h-5 w-5" />
            Add New Tool
          </Button>
        </div>
      </div>

      <ToolSearch
        isSearching={isSearching}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
      />

      <ToolGrid
        tools={filteredTools}
        isLoading={isLoading}
        isSearching={isSearching}
        isPaginating={isPaginating}
        searchTerm={searchTerm}
        onToolClick={handleCardClick}
        onToolDelete={handleDelete}
        onToolFork={handleFork}
        deletingToolId={deletingToolId || undefined}
      />

      <ToolPagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalTools={totalTools}
        onPageChange={setCurrentPage}
        isPaginating={isPaginating}
      />

      <ToolConfigurationDrawer
        isOpen={drawerOpen}
        onClose={handleDrawerClose}
        tool={isForking ? null : selectedTool}
        toolToFork={isForking ? selectedTool : null}
      />
    </div>
  );
};

const ToolsManagement = () => (
  <ToolsProvider>
    <ToolsManagementContent />
  </ToolsProvider>
);

export default ToolsManagement;
