import { ApiService, ApiError } from "@/lib/api";
import { TeamResponse } from "@/types/team";

// Message configuration structure
export interface MessageConfig {
  source: string;
  message_type: string;
  content: any;
}

// Individual message structure
export interface MessageItem {
  id: string | null;
  run_id: string;
  session_id: string;
  config: MessageConfig;
  created_at: string;
  updated_at: string | null;
  is_deleted: boolean;
}

// Session structure containing messages
export interface MessageType {
  id: string;
  run_id: string | null;
  created_at: string;
  description?: string;
  updated_at: string | null;
  name: string;
  team_id: string;
  team: TeamResponse;
  is_deleted: boolean;
  messages: MessageItem[];
}

class ChatMessageService {
  /**
   * Get messages for a specific session
   * @param sessionId - The session ID to fetch messages for
   * @returns Promise<MessageType[]> - Array of messages for the session
   */
  static async getMessagesBySession(sessionId: string): Promise<MessageType> {
    try {
      const response = await ApiService.get<MessageType>(
        `/private/sessions/${sessionId}`
      );
      return response;
    } catch (error) {
      const apiError = error as ApiError;
      throw new Error(apiError.message || "Failed to fetch messages");
    }
  }

  static async getRunId(sessionId: string, userId: string): Promise<RunData> {
    try {
      const data = {
        session_id: sessionId,
        user_id: userId,
      };
      const response = await ApiService.post<RunData>(
        `/private/sessions/generate-run`,
        data
      );
      return response;
    } catch (error) {
      const apiError = error as ApiError;
      throw new Error(apiError.message || "Failed to fetch messages");
    }
  }

  static async sendMessage(runId: string, payload: any): Promise<any> {
    try {
      console.log("DEBUG: Sending message:", payload);
      const response = await ApiService.post<any>(
        `/private/sessions/chat/${runId}`,
        payload
      );
      return response;
    } catch (error) {
      const apiError = error as ApiError;
      throw new Error(apiError.message || "Failed to fetch messages");
    }
  }

  static async getWorkflowStatus(runId: string): Promise<any> {
    try {
      const response = await ApiService.get<any>(
        `/private/sessions/chat/${runId}`
      );
      return response;
    } catch (error) {
      const apiError = error as ApiError;
      throw new Error(apiError.message || "Failed to get workflow status");
    }
  }
}

export default ChatMessageService;
