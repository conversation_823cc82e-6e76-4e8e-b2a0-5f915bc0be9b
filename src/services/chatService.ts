import { ApiService, API_ENDPOINTS, ApiError } from "@/lib/api";
import { ChatTypes, ChatItem } from "@/types/chat";

// Query parameters interface for sessions endpoint
export interface SessionsQueryParams {
  team_id?: string;
  page?: number;
  size?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  name?: string;
  name_like?: string;
  is_active?: boolean;
}

export interface CreateSessionRequest {
  name: string;
  team_id: string;
}

export interface UpdateSessionRequest {
  name: string;
}

export interface PresignedURLRequest {
  object_name: string;
  expiration_minutes?: number;
  content_type?: string;
}

export interface PresignedURLResponse {
  presigned_url: string;
  provider: 'aws' | 'azure';
  bucket_name?: string;
  container_name?: string;
  object_name: string;
  expires_in_minutes: number;
}

class ChatService {
  /**
   * Get all sessions with optional query parameters
   * @param params - Query parameters for filtering and pagination
   * @returns Promise<ChatTypes> - Sessions data with pagination info
   */
  static async getSessions(params?: SessionsQueryParams): Promise<ChatTypes> {
    try {
      const response = await ApiService.get<ChatTypes>(
        API_ENDPOINTS.PRIVATE.SESSIONS,
        { params }
      );
      return response;
    } catch (error) {
      const apiError = error as ApiError;
      throw new Error(apiError.message || "Failed to fetch sessions");
    }
  }

  /**
   * Get sessions for a specific team
   * @param teamId - The team ID to filter sessions
   * @param additionalParams - Additional query parameters
   * @returns Promise<ChatTypes> - Sessions data for the team
   */
  static async getSessionsByTeam(
    teamId: string,
    additionalParams?: Omit<SessionsQueryParams, 'team_id'>
  ): Promise<ChatTypes> {
    return this.getSessions({
      team_id: teamId,
      ...additionalParams
    });
  }

  static async createSession(sessionData: CreateSessionRequest): Promise<ChatItem> {
    try {
      const response = await ApiService.post<ChatItem>(
        API_ENDPOINTS.PRIVATE.SESSIONS,
        sessionData
      );
      return response;
    } catch (error) {
      const apiError = error as ApiError;
      throw new Error(apiError.message || "Failed to create session");
    }
  }

  static async updateSession(sessionId: string, sessionData: UpdateSessionRequest): Promise<ChatItem> {
    try {
      const response = await ApiService.put<ChatItem>(
        `${API_ENDPOINTS.PRIVATE.SESSIONS}/${sessionId}`,
        sessionData
      );
      return response;
    } catch (error) {
      const apiError = error as ApiError;
      throw new Error(apiError.message || "Failed to update session");
    }
  }

  static async deleteSession(sessionId: string): Promise<void> {
    try {
      await ApiService.delete(`${API_ENDPOINTS.PRIVATE.SESSIONS}/${sessionId}`);
    } catch (error) {
      const apiError = error as ApiError;
      throw new Error(apiError.message || "Failed to delete session");
    }
  }

  /**
   * Generate a presigned URL for file upload
   * @param presignedData - Request data for presigned URL generation
   * @returns Promise<PresignedURLResponse> - Presigned URL response with provider info
   */
  static async generatePresignedURL(presignedData: PresignedURLRequest): Promise<PresignedURLResponse> {
    try {
      const response = await ApiService.post<PresignedURLResponse>(
        API_ENDPOINTS.PRIVATE.CHAT_PRESIGNED,
        presignedData
      );
      return response;
    } catch (error) {
      const apiError = error as ApiError;
      throw new Error(apiError.message || "Failed to generate presigned URL");
    }
  }

  /**
   * Upload file to presigned URL
   * @param presignedUrl - The presigned URL to upload to
   * @param file - The file to upload
   * @param contentType - The content type of the file
   * @returns Promise<void>
   */
  static async uploadFileToPresignedURL(
    presignedUrl: string,
    file: File,
    contentType?: string
  ): Promise<void> {
    try {
      const headers: Record<string, string> = {};

      // Set content type if provided
      if (contentType) {
        headers['Content-Type'] = contentType;
      }

      // For AWS, we need to use PUT method
      // For Azure, we also use PUT method
      await ApiService.requestFullUrl<void>(presignedUrl, {
        method: 'PUT',
        data: file,
        headers,
      });
    } catch (error) {
      const apiError = error as ApiError;
      throw new Error(apiError.message || "Failed to upload file");
    }
  }
}

export default ChatService;
