import { TeamResponse } from "./team";

enum ComponentType {
  TextInput = "TextInput",
  FileInput = "FileInput",
  URLInput = "URLInput",
}

export type ChatItem = {
  id: string;
  created_at: Date;
  updated_at: Date;
  name: string;
  description?: string;
  is_deleted: boolean;
  type?: ComponentType;
  team_id: string;
  team: TeamResponse;
};

export type ChatTypes = {
  items: ChatItem[];
  total: number;
  page: number;
  pages: number;
  size: number;
};
