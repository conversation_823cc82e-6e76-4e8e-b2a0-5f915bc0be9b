export interface KnowledgeBase {
  id: string;
  name: string;
  description?: string;
  folder_id: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  file_count: number;
  chunking_config: string; // JSON string
  embedding_config: string; // JSON string
  vector_db_config: string; // JSON string
}

export interface KnowledgeBaseCreate {
  knowledgebase_name: string;
  folder_description?: string;
  chunking_config?: string; // JSON string
  embedding_config?: string; // JSON string
  vector_db_config?: string; // JSON string
  files?: File[];
}

export interface KnowledgeBaseUpdate {
  name?: string;
  description?: string;
  is_active?: boolean;
  chunking_config?: object;
  embedding_config?: object;
  vector_db_config?: object;
}

export interface KnowledgeBaseResponse {
  folder_id: string;
  folder_name: string;
  uploaded_files: Array<{
    file_id: string;
    filename: string;
    s3_path: string;
    status: string;
    size: number;
    content_type: string;
  }>;
  total_files: number;
  successful_uploads: number;
  failed_uploads: number;
}

export interface KnowledgeBaseList {
  items: KnowledgeBase[];
  total: number;
  page: number;
  pages: number;
  size: number;
}

export interface KnowledgeBaseFilters {
  skip?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
  name?: string;
  name_like?: string;
  is_active?: boolean;
}

export interface BulkUploadResponse {
  uploaded_files: Array<{
    file_id: string;
    filename: string;
    s3_path: string;
    status: string;
    size: number;
    content_type: string;
  }>;
  total_files: number;
  successful_uploads: number;
  failed_uploads: number;
}

export interface KnowledgeBaseFileMetadata {
  file_id: string;
  filename: string;
  content_type: string;
  size: number;
  last_modified?: string;
  etag?: string;
  s3_path: string;
  status: string;
}

export interface KnowledgeBaseFilesResponse {
  files: KnowledgeBaseFileMetadata[];
  total_files: number;
  folder_id: string;
}
