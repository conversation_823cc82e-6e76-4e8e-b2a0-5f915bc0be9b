
export type EnvironmentVariableType = "string" | "number" | "boolean" | "secret";

export interface EnvironmentVariableCreate {
    user_id: string;
    name: string;
    value: string;
    type?: EnvironmentVariableType;
    description?: string | null;
    required?: boolean;
}

export interface EnvironmentVariableUpdate {
    name?: string;
    value?: string;
    type?: EnvironmentVariableType;
    description?: string | null;
    required?: boolean;
}

export interface EnvironmentVariableOut {
    id: string;
    user_id: string;
    name: string;
    value?: string;
    type: EnvironmentVariableType;
    description?: string | null;
    required: boolean;
}

export interface PaginatedEnvVarResponse {
    items: EnvironmentVariableOut[];
    total: number;
    page: number;
    pages: number;
    size: number;
}
