// Template creation request (matches backend TeamTemplateCreate)
export interface TemplateCreateRequest {
  name: string;
  description?: string;
  category?: string;
  template_config: any; // Complex nested structure for team, inputs, outputs
  use_case?: string;
  tags?: string[];
}

// Template update request (matches backend TeamTemplateUpdate)
export interface TemplateUpdate {
  name?: string;
  description?: string;
  category?: string;
  template_config?: any;
  use_case?: string;
  tags?: string[];
}

// Template response from API (matches backend TeamTemplateResponse)
export interface TemplateResponse {
  id: string;
  name: string;
  description?: string;
  category?: string;
  template_config: any; // Complex nested structure
  use_case?: string;
  tags?: string[];
  organization_id?: string;
  created_at: string;
  updated_at?: string;
  is_deleted: boolean;
}

// Template list response (matches backend TeamTemplateList)
export interface TemplateListResponse {
  items: TemplateResponse[];
  total: number;
  page: number;
  pages: number;
  size: number;
}

// Template filters for API queries (matches backend query parameters)
export interface TemplateFilters {
  skip?: number;
  limit?: number;
  sort_by?: string[];
  sort_order?: "asc" | "desc";
  category?: string;
  tags?: string[];
  search?: string; // Will be mapped to name_like in API
}

// Template creation payload for workflow instantiation (matches backend TeamFromTemplateRequest)
export interface TemplateInstantiationRequest {
  template_id: string;
  name?: string;
  customizations?: Record<string, any>;
}

// Template instantiation response
export interface TemplateInstantiationResponse {
  team_id: string;
  template_id: string;
  status: "success" | "error";
  message?: string;
}

// Template categories enum - common categories that could be used
export enum TemplateCategory {
  TEXT_GENERATION = "Text Generation",
  CONVERSATIONAL_AI = "Conversational AI",
  DATA_ANALYSIS = "Data Analysis",
  PRODUCTIVITY = "Productivity",
  COMPUTER_VISION = "Computer Vision",
  DEVELOPMENT = "Development",
  CUSTOM = "Custom",
  AUTOMATION = "Automation",
  CONTENT_CREATION = "Content Creation",
  RESEARCH = "Research",
}

// Legacy types for backward compatibility
export interface Template extends TemplateResponse {}
export interface TemplateList extends TemplateListResponse {}